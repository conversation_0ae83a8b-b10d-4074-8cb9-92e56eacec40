<template>
    <div class="">
        <div v-loading="loading" class="tables">
            <el-table
                v-if="tableData.length"
                :data="tableData"
                style="width: 100%"
                class="tables"
                height="440"
            >
                <!--    -->
                <el-table-column
                    prop="date"
                    :label="$t('common_riqi')"
                    width="150"
                    align="center"
                    fixed="left"
                />
                <!--  v-if="type == 'Fee'" -->
                <el-table-column
                    prop="profit"
                    :label="t('station_chongfangdianshouyi')"
                    align="center"
                />
                <el-table-column
                    :label="
                        t('chongdianchengben') +
                        (locale == 'en' ? '' : '(' + t('common_yuan') + ')')
                    "
                    align="center"
                >
                    <el-table-column
                        prop="jdChargeFee"
                        :label="$t('Peak')"
                        align="center"
                    />
                    <el-table-column
                        prop="fdChargeFee"
                        :label="$t('OnPeak')"
                        align="center"
                    />
                    <el-table-column
                        prop="pdChargeFee"
                        :label="$t('Flat')"
                        align="center"
                    />
                    <el-table-column
                        prop="gdChargeFee"
                        :label="$t('Valley')"
                        align="center"
                    />
                    <!-- <el-table-column
                        prop="gdChargeFee"
                        label="深谷"
                        
                        align="center"
                    /> -->
                    <el-table-column
                        prop="chargeFee"
                        :label="$t('station_zongji')"
                        align="center"
                    />
                </el-table-column>
                <el-table-column
                    :label="
                        t('fangdianshouyi') +
                        (locale == 'en' ? '' : '(' + t('common_yuan') + ')')
                    "
                    align="center"
                >
                    <el-table-column
                        prop="jdDischargeFee"
                        :label="$t('Peak')"
                        align="center"
                    />
                    <el-table-column
                        prop="fdDischargeFee"
                        :label="$t('OnPeak')"
                        align="center"
                    />
                    <el-table-column
                        prop="pdDischargeFee"
                        :label="$t('Flat')"
                        align="center"
                    />
                    <el-table-column
                        prop="gdDischargeFee"
                        :label="$t('Valley')"
                        align="center"
                    />
                    <!-- <el-table-column
                        prop="gdDischargeFee"
                        label="深谷"
                        
                        align="center"
                    /> -->
                    <el-table-column
                        prop="dischargeFee"
                        :label="$t('station_zongji')"
                        align="center"
                    />
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup>
import { watch, nextTick, ref } from 'vue'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
// 日期选择⬆️
const props = defineProps({
    tableData: {
        type: Array,
        default: () => [],
    },
    type: {
        type: String,
        default: '',
    },
    loading: {
        type: Boolean,
        default: false,
    },
})
const setTableData = () => {
    if (props.tableData && props.tableData.length) {
        //
    }
}
watch(
    () => props.tableData,
    () => {
        nextTick(() => {
            //
            setTableData()
        })
    }
)
</script>

<style lang="less" scoped>
.search {
    width: calc(~'100% - 420px');
}
</style>
