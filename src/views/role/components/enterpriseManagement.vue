<template>
    <div class="enterprise-management">
        <el-table
            :data="dataSource"
            v-loading="loading"
            :row-key="(record) => record.staffId"
            class="alarm-table"
            @row-click="handleRowClick"
            style="width: 100%"
        >
            <el-table-column
                prop="realName"
                :label="$t('user_xingming')"
                min-width="200"
            >
                <template #default="{ row }">
                    <div class="bold-box">
                        {{ row.realName }}
                        <!-- <span v-if="row.owner" class="user-type-tag">
                            {{ $t('enterprise_zhuguanliyuan') }}
                        </span> -->
                        <span
                            v-if="getUserTypeLabelByRoleIds(row.roleIds)"
                            class="user-type-tag"
                        >
                            {{ getUserTypeLabelByRoleIds(row.roleIds) }}
                        </span>
                    </div>
                </template>
            </el-table-column>

            <el-table-column
                prop="phone"
                :label="$t('phone')"
                min-width="120"
                align="center"
            />

            <el-table-column
                prop="email"
                :label="$t('Email')"
                min-width="160"
                align="center"
            />

            <el-table-column
                prop="permissions"
                :label="$t('user_EMSkongzhiquanxian')"
                min-width="120"
                align="center"
            >
                <template #default="{ row }">
                    <CheckOutlined
                        v-if="
                            row.permissions &&
                            row.permissions.includes('emsControl')
                        "
                    />
                </template>
            </el-table-column>

            <el-table-column
                prop="resourceScope"
                :label="$t('user_DataRange')"
                min-width="100"
                align="center"
            >
                <template #default="{ row }">
                    {{
                        row.resourceScope == 'all'
                            ? $t('allData')
                            : $t('OnlySelectedData')
                    }}
                </template>
            </el-table-column>

            <el-table-column
                prop="lastLoginTime"
                :label="$t('user_shangcidenglushujian')"
                min-width="140"
                align="right"
            />
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-wrapper">
            <el-pagination
                background
                layout="prev, pager, next"
                :total="pagination.total"
                v-model:current-page="pagination.current"
                :page-size="pagination.size"
                @change="handlePageChange"
            />
        </div>

        <el-drawer
            v-model="visible"
            :size="486"
            :show-close="false"
            @close="onClose"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{
                            edit ? $t('Edit User') : $t('Add User')
                        }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="onClose">{{
                            $t('Cancle')
                        }}</el-button>

                        <confirm-button
                            v-if="
                                hasEditPermission && edit
                                    ? clickData?.owner
                                        ? false
                                        : true
                                    : false
                            "
                            :title="$t('Are you sure to delete')"
                            @confirm="onConfirm"
                        >
                            <template #reference>
                                <el-button plain round>{{
                                    $t('Delete')
                                }}</el-button>
                            </template>
                        </confirm-button>
                        <el-button
                            v-if="hasEditPermission"
                            plain
                            type="primary"
                            round
                            :loading="addLoading"
                            @click="submit"
                            >{{ $t('Save') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <addUser
                ref="addUserForm"
                :data="clickData"
                :sites="AllSites"
                :userRoles="userRoles"
                :hasEditPermission="hasEditPermission"
            />
        </el-drawer>
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { usePagenation } from '@/common/setup'
import apiService from '@/apiService/device'
import addUser from './addUser.vue'
// import enterpriseDetailTop from './enterpriseDetailTop.vue'
import { message } from 'ant-design-vue'
import { CheckOutlined } from '@ant-design/icons-vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const store = useStore()
const dataSource = ref([])
const loading = ref(false)
const visible = ref(false)
const addUserForm = ref(null)
const edit = ref(false)
const addLoading = ref(false)
const clickData = ref(void 0)

// 权限控制
const isOrgOwner = computed(() => {
    return store.state.user.userInfoData.roles.includes('org_owner')
})
const isSubAdmin = computed(() => {
    return store.state.user.userInfoData.roles.includes('org_sub_admin')
})
const hasEditPermission = computed(() => {
    return isOrgOwner.value || isSubAdmin.value
})

// 处理行点击事件
const handleRowClick = (record) => {
    clickData.value = record
    edit.value = true
    visible.value = true
}
const pagination = ref({ current: 1, size: 10, total: 0 })
const getStaffPageData = async () => {
    const params = {
        current: pagination.value.current,
        size: pagination.value.size,
    }
    try {
        loading.value = true
        const {
            data: {
                data: { records, total },
            },
        } = await apiService.getStaffPage(params)
        loading.value = false
        dataSource.value = records || []
        pagination.value.total = total || 0
    } catch (error) {
        loading.value = false
    }
}

// 处理当前页变化

const handlePageChange = async (page) => {
    pagination.value.current = page
    getStaffPageData()
}

const addUserFun = (boolean) => {
    visible.value = true
    edit.value = boolean
    clickData.value = void 0
}

const onClose = () => {
    addUserForm.value.resetFields()
    addUserForm.value.clearValidate()
    addLoading.value = false
    visible.value = false
    clickData.value = void 0
}

const addStaff = async (params) => {
    console.log('params---', params)
    if (params.resourceScope == 'all') {
        params.viewableStationIds = undefined
    }
    try {
        addLoading.value = true
        const {
            data: { code },
        } = await apiService.addStaff(params)
        if (code === 0) {
            message.success(t('Successed'))
            await getStaffPageData()
            onClose()
        }
        // if (code === 10015) {
        //     message.error(t('The phone number has already been registered'))
        // }
        addLoading.value = false
    } catch (error) {
        addLoading.value = false
    }
}

const editModifyStaff = async (params) => {
    try {
        addLoading.value = true
        const {
            data: { code },
        } = await apiService.editModifyStaff(params)
        if (code === 0) {
            message.success(t('Successed'))
            await getStaffPageData()
            onClose()
        }
        addLoading.value = false
    } catch (error) {
        addLoading.value = false
    }
}

const submit = async () => {
    try {
        if (!addUserForm.value) {
            console.error('addUserForm ref is not available')
            return
        }

        const isValid = await addUserForm.value.submitRules()
        console.log('validation result---', isValid)
        if (isValid) {
            // 获取表单数据
            const formData = addUserForm.value.formState
            if (!edit.value) {
                addStaff(formData)
            } else {
                editModifyStaff({
                    ...formData,
                    staffId: clickData.value.staffId,
                })
            }
        }
    } catch (error) {
        console.error('Error in submit:', error)
    }
}

const btnLoading = ref(false)

const onConfirm = async () => {
    btnLoading.value = true
    const {
        data: { code },
    } = await apiService.removeStaff({
        staffId: clickData.value.staffId,
    })
    if (code === 0) {
        message.success(t('Successed'))
        btnLoading.value = false
        onClose()
    }
}

defineExpose({ addUserFun })

const AllSites = ref([])
const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])

const orgId = ref(
    getCompanyInfo?.value?.orgId ? getCompanyInfo.value.orgId : void 0
)

// 数据处理方法
const processTreeData = (nodes) => {
    return nodes.map((node) => ({
        ...node,
        disabled: node.resourceType != 'station',
        children: node.children ? processTreeData(node.children) : [],
    }))
}
const getAllSites = async () => {
    //
    // const res = await apiService.getSubSupplierWithOwnerTree()
    const res = await apiService.getOrgWithStationTopology({
        orgId: orgId.value,
    })
    console.log(res)
    AllSites.value = processTreeData([res.data.data])
}
const userRoles = ref([
    // {
    //     value: 1,
    //     label: t('Administrator'),
    // },
    {
        value: '2',
        label: t('Standard User'),
    },
    {
        value: '5',
        label: t('Sub Admin'),
    },
])

// 根据roleIds数组获取用户类型标签
const getUserTypeLabelByRoleIds = (roleIds) => {
    if (!roleIds || !Array.isArray(roleIds)) {
        return ''
    }

    // 优先级：1(主管理员) > 2(子管理员) > 其他
    if (roleIds.includes('1')) {
        return t('enterprise_zhuguanliyuan')
    } else if (roleIds.includes('5')) {
        return t('Sub Admin')
    }
    // else if (roleIds.includes('2')) {
    //     return t('Standard User')
    // }

    return ''
}

onMounted(() => {
    getStaffPageData()
    getAllSites()
})
</script>

<style scoped lang="less">
.enterprise-management {
    // margin-top: 16px;
    width: 100%;
    height: calc(~'100vh - 253px');
    display: flex;
    flex-direction: column;
    :deep(.el-table__inner-wrapper) {
        flex: 1;
        .el-table__body-wrapper {
            overflow: auto;
        }
    }
    .table-title {
        font-size: 16px;
        color: #222222;
        margin-top: 28px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .bt {
            display: flex;
            align-items: center;
        }

        :deep(.addUser) {
            width: 20px;
            height: 20px;
        }
    }
    .alarm-table {
        // max-height: calc(100% - 80px);
        max-height: calc(~'100% - 48px');
        width: 100%;

        :deep(.el-table__row) {
            cursor: pointer;
        }

        :deep(th.el-table__cell) {
            background: none;
            font-weight: normal;
            color: var(--text-60) !important;
            padding: 12px 0;
        }

        :deep(td.el-table__cell) {
            padding: 12px 0;
            font-size: 14px;
            color: var(--text-80);
        }
        :deep(.cell) {
            line-height: 26px;
        }
    }

    .pagination-wrapper {
        display: flex;
        justify-content: center;
        padding-top: 16px;
    }

    .bt {
        padding: 4px 15px;
        font-size: 14px;
        height: 32px;
    }

    .bold-box {
        // font-size: 16px !important;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .user-type-tag {
        padding: 0 8px;
        background: rgba(22, 119, 255, 0.1);
        border-radius: 2px;
        height: 22px;
        color: #1677ff;
        font-size: 12px;
        line-height: 22px;
        display: inline-flex;
        align-items: center;
    }
}
:deep(.el-dialog) {
    background: var(--input-bg);
    backdrop-filter: blur(10px);
    .el-dialog__title {
        color: var(--text-100);
    }
    .el-dialog__headerbtn .el-dialog__close {
        color: var(--text-60);
    }
    .el-dialog__body {
        color: var(--text-80);
    }
}
</style>

<style lang="less">
.ant-modal-confirm-btns {
    float: none;
    text-align: center;
}

.my-modal {
    width: 350px !important;

    .ant-modal-confirm-title {
        font-size: 16px;

        + .ant-modal-confirm-content {
            margin-left: 38px;
        }
    }

    .ant-modal-confirm-content {
        margin-top: 8px;
        font-size: 14px;
    }

    .ant-modal-body {
        padding: 32px 32px 24px;
    }

    .ant-modal-confirm-btns {
        float: none;
        margin-top: 24px;

        .ant-btn {
            height: 32px;
            line-height: 16px;
            font-size: 14px;
            padding: 6px 12px;
            box-sizing: border-box;
            background: #f5f7f7;
            color: var(--text-100);
            border-color: #f5f7f7;

            &:hover {
                border-color: #f5f7f7;
            }
        }

        .ant-btn-primary {
            color: #fff;
            border-color: var(--themeColor);
            background-color: var(--themeColor);

            &:hover {
                color: #fff;
                border-color: var(--themeColor);
            }
        }
    }
}
</style>
