<template>
    <div class="sub-account">
        <el-table
            :data="dataSource"
            row-key="id"
            default-expand-all
            style="width: 100%"
            @row-click="handleRowClick"
            class="alarm-table"
        >
            <el-table-column
                prop="name"
                :label="$t('OrganizationName')"
                width="400"
                fixed
            />
            <el-table-column
                prop="adminName"
                :label="$t('sub_guanliyuanxingming')"
                align="center"
                width="220"
            />
            <el-table-column
                prop="adminPhone"
                :label="$t('tell')"
                align="center"
                width="160"
            />
            <el-table-column
                prop="adminEmail"
                :label="$t('Email')"
                align="center"
                width="220"
            />
            <el-table-column prop="businessType" align="center" width="220">
                <template #header>
                    <div class="flex items-center justify-center gap-2">
                        <span>{{ $t('sub_yewufanwei') }}</span>
                        <!-- 
                        <el-button
                            size="small"
                            type="primary"
                            plain
                            @click="handleBusinessTypeChange"
                        >
                            {{ getBusinessTypeLabel(currentBusinessType) }}
                        </el-button> -->
                        <div
                            @click="handleBusinessTypeChange"
                            class="cursor-pointer"
                        >
                            <iconSvg
                                class="w-5 h-5 align-middle mr-1"
                                name="toggle"
                                className="icon-search"
                            />
                        </div>
                    </div>
                </template>
                <template #default="{ row }">
                    {{ getBusinessType(row.businessType) }}
                </template>
            </el-table-column>
            <el-table-column
                prop="orgType"
                :label="$t('sub_sfjyktzzhqx')"
                align="center"
                :width="locale == 'en' ? 208 : 180"
            >
                <template #default="{ row }">
                    {{
                        row.orgType == 'supplier'
                            ? $t('common_shi')
                            : $t('common_fou')
                    }}
                </template>
            </el-table-column>
            <el-table-column
                prop="createTime"
                :label="$t('sub_tianjiashijian')"
                width="180"
                align="center"
            />
            <el-table-column
                prop="operation"
                :label="$t('operation')"
                align="center"
                width="90"
                fixed="right"
            >
                <template #default="{ row }">
                    <el-dropdown
                        trigger="hover"
                        :visible="dropVisible"
                        @click.stop
                        class="operation-column"
                    >
                        <div class="operation-icon">
                            <iconSvg
                                name="arrowdrop"
                                class="w-4 h-4"
                                @click.stop
                            />
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="addNewOrg(row)">{{
                                    $t('Add Sub-account')
                                }}</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-table-column>
        </el-table>

        <el-drawer
            v-model="visible"
            :size="486"
            :show-close="false"
            @close="onClose"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{
                            currentRecord?.openType == 'edit'
                                ? $t('Edit Sub-account')
                                : $t('Add Sub-account')
                        }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="onClose">{{
                            $t('Cancle')
                        }}</el-button>
                        <confirm-button
                            v-if="currentRecord?.openType == 'edit'"
                            :title="$t('Are you sure to delete')"
                            @confirm="onDelete"
                            placement="bottom-end"
                        >
                            <template #reference>
                                <el-button
                                    plain
                                    round
                                    :disabled="
                                        currentRecord?.children?.length > 0
                                    "
                                    >{{ $t('Delete') }}</el-button
                                >
                            </template>
                        </confirm-button>

                        <el-button
                            plain
                            type="primary"
                            round
                            :loading="addLoading"
                            @click="submit"
                            >{{ $t('Save') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <subAccount
                ref="subAccountBox"
                v-model:selectedOrg="currentRecord"
            />
        </el-drawer>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import apiService from '@/apiService/device'
import api from '@/apiService/index'

import subAccount from './addSubAccount.vue'
import { message } from 'ant-design-vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const dataSource = ref([])

const loading = ref(false)

const visible = ref(false)

const addLoading = ref(false)

const subAccountBox = ref(null)
const dropVisible = ref(false)

// 当前选中的业务类型
const currentBusinessType = ref('energy_storage_cabinet')

// 定义emit事件
const emit = defineEmits(['businessTypeChange'])

const businesses = [
    {
        value: 'all',
        label: t('All'),
    },
    {
        value: 'energy_storage_cabinet',
        label: t('sub_options_gongshangyechuneng'),
    },
    {
        value: 'vehicle_battery',
        label: t('sub_options_donglidianchi'),
    },
]

const getBusinessType = (type) => {
    if (!type) return '-'
    let res = businesses.find((item) => item.value == type)
    return res ? res.label : '-'
}

// 获取当前业务类型的标签
const getBusinessTypeLabel = (type) => {
    if (type === 'energy_storage_cabinet') {
        return t('sub_options_gongshangyechuneng')
    } else if (type === 'vehicle_battery') {
        return t('sub_options_donglidianchi')
    }
    return t('All')
}

// 处理业务类型切换
const handleBusinessTypeChange = () => {
    // 在energy_storage_cabinet和vehicle_battery之间切换
    currentBusinessType.value =
        currentBusinessType.value === 'energy_storage_cabinet'
            ? 'vehicle_battery'
            : 'energy_storage_cabinet'

    // 向父组件抛出事件
    emit('businessTypeChange', currentBusinessType.value)

    // 重新获取数据
    getStaffPageData()
}
// 数据处理方法
const processTreeData = (nodes, depth = 0) => {
    return nodes.map((node) => ({
        ...node,
        hasChildren: node.children?.length > 0,
        children: node.children ? processTreeData(node.children) : [],
    }))
}
const getStaffPageData = async () => {
    const params = {
        // current: pageParam.value.current,
        // size: pageParam.value.size,
        businessType: currentBusinessType.value,
    }
    try {
        loading.value = true
        const res = await apiService.getSubSupplierWithOwnerTree(params)
        loading.value = false
        dataSource.value = res.data.data
    } catch (error) {
        loading.value = false
    }
}

const onClose = () => {
    currentRecord.value = {}
    subAccountBox.value.clearValidate()
    subAccountBox.value.resetFields()
    visible.value = false
}

const createSubOrgWithOwner = async (params) => {
    try {
        console.log(params, '创建')
        addLoading.value = true
        params.orgType = params.orgType ? 'supplier' : 'customer'
        const {
            data: { code, msg },
        } = await apiService.createSubOrgWithOwner(params)
        if (code === 0) {
            message.success(t('Successed'))
            onClose()
            // 加载列表
            getStaffPageData()
        } else {
            // message.error(msg)
        }
        addLoading.value = false
    } catch (error) {
        addLoading.value = false
    }
}
const modifySubOrgWithOwner = async (params) => {
    try {
        addLoading.value = true
        console.log(params, '编辑')

        // 编辑时只传递orgId和orgName
        let res = await apiService.modifyOrgInfo({
            orgId: params.orgId,
            orgName: params.orgName,
        })
        if (res.data.code === 0) {
            message.success(t('Successed'))
            onClose()
            // 加载列表
            getStaffPageData()
        }
        addLoading.value = false
    } catch (error) {
        addLoading.value = false
    }
}
const onDelete = async () => {
    let res = await api.delOrg(currentRecord.value.id)
    if (res.data.code === 0) {
        message.success(t('Successed'))
        onClose()
        getStaffPageData()
    } else {
        //
    }
}

const submit = async () => {
    try {
        // 调用子组件的方法并获取数据
        const formState = await subAccountBox.value.submitRules()
        console.log(formState, 'formState')

        if (currentRecord.value.openType == 'add') {
            createSubOrgWithOwner(formState)
        } else {
            modifySubOrgWithOwner(formState)
        }
    } catch (error) {
        // 这里处理验证失败的逻辑
        console.error('Submit failed:', error)

        // 显示用户友好的错误消息
        if (error.message) {
            // message.error(error.message)
        } else {
            // message.error(t('Validation failed, please check your input'))
        }
    }
}

onMounted(() => {
    getStaffPageData()
})

const currentRecord = ref()
const addNewOrg = (row) => {
    currentRecord.value = { ...row, openType: 'add' }
    visible.value = true
}
const findParentNode = (tree, parentId) => {
    // 如果 parentId 为 null，直接返回 null
    if (parentId == null) return null
    // 递归遍历树
    const traverse = (nodes) => {
        for (const node of nodes) {
            if (node.id === parentId) {
                return node // 找到匹配的父节点
            }
            if (node.children) {
                const found = traverse(node.children)
                if (found) return found
            }
        }
        return null
    }

    return traverse(tree)
}
const handleRowClick = (e) => {
    const parentNode = findParentNode(dataSource.value, e.parentId)
    currentRecord.value = {
        ...e,
        openType: 'edit',
        parentName: e.parentId != '0' ? parentNode?.name : e.name,
    }
    visible.value = true
}
defineExpose({ visible })
</script>

<style lang="less" scoped>
.sub-account {
    height: calc(~'100vh - 253px');
    display: flex;
    flex-direction: column;
    :deep(.el-table__inner-wrapper) {
        flex: 1;
        .el-table__body-wrapper {
            overflow: auto;
        }
    }
    .alarm-table {
        height: 100%;
        width: 100%;
        :deep(.ant-table-row) {
            cursor: pointer;
        }

        :deep(.ant-table-thead > tr > th) {
            background: none;
            font-weight: none;
            color: var(--text-60) !important;
        }

        :deep(.ant-table-tbody > tr > td) {
            padding: 16px;
            font-size: 14px;
            color: var(--text-80);
        }
        :deep(.cell) {
            line-height: 26px;
        }
    }
}
:deep(.el-table__expand-icon) {
    color: var(--text-100);
}
.add-btn {
    color: var(--themeColor);
}
:deep(.el-table .el-table__cell) {
    padding: 12px 0;
}
:deep(.el-table .el-table__cell .cell) {
    align-items: center;
    vertical-align: middle;
}
.operation-column {
    vertical-align: middle;
}
</style>
