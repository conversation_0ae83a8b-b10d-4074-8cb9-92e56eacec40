<template>
    <div class="relative z-20 log-table">
        <el-table
            :data="dataSource"
            row-key="id"
            default-expand-all
            style="width: 100%"
            class="alarm-table"
        >
            <el-table-column
                prop="content"
                :label="$t('操作内容')"
                min-width="540"
            />
            <el-table-column
                prop="type"
                :label="$t('事件类型')"
                min-width="350"
            >
                <template #default="{ row }">
                    {{ getOperationName(row.operationLogModule) }} -
                    {{ row.operationLogTypeName }}
                </template>
            </el-table-column>
            <el-table-column
                prop="staffName"
                :label="$t('操作人')"
                min-width="140"
            />
            <el-table-column
                prop="createTime"
                :label="$t('发生时间')"
                min-width="300"
                align="right"
            />
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-wrapper">
            <el-pagination
                background
                layout="prev, pager, next"
                :total="pagination.total"
                v-model:current-page="pagination.current"
                :page-size="pagination.size"
                @change="handlePageChange"
            />
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import apiVpp from '@/apiService/vpp'
import api from '@/apiService'

// 定义props
const props = defineProps({
    operationLogModule: {
        type: String,
        default: '',
    },
    dateSelect: {
        type: Object,
        default: () => ({
            periodType: 'day',
            startDate: '',
            endDate: '',
        }),
    },
})

// 定义emit
const emit = defineEmits(['update:dataSource'])

const dataSource = ref([])
const loading = ref(false)
const pagination = ref({ current: 1, size: 10, total: 0 })

const getData = async () => {
    const params = {
        current: pagination.value.current,
        size: pagination.value.size,
    }
    try {
        loading.value = true
        const res = await apiVpp.getOperationLogPage({
            ...params,
            ...props.dateSelect,
            operationLogModule: props.operationLogModule,
        })
        loading.value = false
        dataSource.value = res.data.data.records || []
        pagination.value.total = res.data.data.total || 0
        // 向父组件发送数据更新
        emit('update:dataSource', dataSource.value)
    } catch (error) {
        loading.value = false
    }
}

// 处理当前页变化
const handlePageChange = async (page) => {
    pagination.value.current = page
    getData()
}

// 监听props变化，重新获取数据
watch(
    () => [props.operationLogModule, props.dateSelect],
    () => {
        pagination.value.current = 1
        getData()
    },
    { deep: true }
)

const options = ref([])
const getTypeList = async () => {
    const res = await api.getDictByType({
        type: 'operationLogModule',
    })
    options.value = res.data.data
}
const getOperationName = (val) => {
    return options.value.find((item) => item.value == val)?.label
}
onMounted(async () => {
    await getTypeList()
    await getData()
})
</script>

<style lang="less" scoped>
// .ant-table-tbody > tr > td {
//     padding: 16px;
//     font-size: 14px;
//     color: var(--text-80);
// }
.log-table {
    width: 100%;
    height: calc(~'100vh - 253px');
    display: flex;
    flex-direction: column;
    :deep(.el-table__inner-wrapper) {
        flex: 1;
        .el-table__body-wrapper {
            overflow: auto;
        }
    }
}

.alarm-table {
    // max-height: calc(100% - 80px);
    max-height: calc(~'100% - 48px');
    width: 100%;

    :deep(.el-table__row) {
        cursor: pointer;
    }

    :deep(th.el-table__cell) {
        background: none;
        font-weight: normal;
        color: var(--text-60) !important;
        padding: 12px 0;
    }

    :deep(td.el-table__cell) {
        padding: 12px 0;
        font-size: 14px;
        color: var(--text-80);
    }
    :deep(.cell) {
        line-height: 26px;
    }
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    padding: 16px 0;
}
</style>
