<template>
    <div class="pt-6">
        <div class="ul-box flex justify-between">
            <ul class="flex flex-1 w-0 bg-title-t title-family">
                <li class="pl-8">
                    <span class="text-sm text-title dark:text-title-dark"
                        >{{ $t('station_yunxingzhuangtai') }}：</span
                    ><span
                        class="origin"
                        v-if="data?.runStatus || data?.runStatus == 0"
                    ></span
                    ><span class="ml-1 text-sm" style="color: var(--text-80)">{{
                        data?.runStatus || data?.runStatus == 0
                            ? runningStatus[data?.runStatus]
                            : '-'
                    }}</span>
                </li>
                <li class="pl-8">
                    <span class="text-sm text-title dark:text-title-dark"
                        >{{ $t('station_chongfangdianzhuangtai') }}：</span
                    ><span
                        class="ml-1 text-sm text-title dark:text-title-dark"
                        >{{
                            data?.chargeStatus || data?.chargeStatus == 0
                                ? batteryStatus[data?.chargeStatus]
                                : '-'
                        }}</span
                    >
                </li>
            </ul>
            <div class="mr-1.5 flex items-center">
                <el-button plain round @click="showBox" linear>{{
                    $t('View Cell Analysis')
                }}</el-button>
            </div>
        </div>
        <div :style="objectStyle" class="flex flex-wrap place-content-start">
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_zongdianya') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.totalVoltage || data?.totalVoltage == 0
                            ? data.totalVoltage + ' V'
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">{{ $t('soh') }}：</div>
                <div class="descriptions-content">
                    {{ data?.soh || data.soh == 0 ? data.soh + ' %' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_zuigaowendu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.maxTemperature || data.maxTemperature == 0
                            ? data.maxTemperature + ' °C'
                            : '-'
                    }}{{
                        data?.maxTemperatureId
                            ? ' | #' + data.maxTemperatureId
                            : ''
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_zongdianliu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.totalElectricity || data.totalElectricity == 0
                            ? data.totalElectricity.toFixed(1) + ' A'
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">{{ $t('soc') }}：</div>
                <div class="descriptions-content">
                    {{ data?.soc || data?.soc == 0 ? data?.soc + ' %' : '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_zuidiwendu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.minTemperature || data?.minTemperature == 0
                            ? data?.minTemperature + ' °C'
                            : '-'
                    }}{{
                        data?.minTemperatureId
                            ? ' | #' + data?.minTemperatureId
                            : ''
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_kechongdianliang') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.chargeCapacity || data?.chargeCapacity == 0
                            ? accountUnit(data?.chargeCapacity, 1000, 'h')
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_zuigaodianya') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.maxVoltage || data.maxVoltage == 0
                            ? data.maxVoltage + ' V'
                            : '-'
                    }}{{
                        data?.maxVoltageId ? ' | #' + data?.maxVoltageId : ''
                    }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_pingjunwendu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.avgTemperature || data.avgTemperature == 0
                            ? data.avgTemperature + ' °C'
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_kefangdianliang') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.dischargeCapacity || data.dischargeCapacity == 0
                            ? accountUnit(data?.dischargeCapacity, 1000, 'h')
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_zuididianya') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.minVoltage || data.minVoltage
                            ? data.minVoltage + ' V'
                            : '-'
                    }}{{
                        data?.minVoltageId ? ' | #' + data?.minVoltageId : ''
                    }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">{{ $t('ins') }}：</div>
                <div class="descriptions-content">
                    {{
                        data?.insulationValue || data.insulationValue == 0
                            ? data.insulationValue + ' kΩ'
                            : '-'
                    }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('station_leijichongdianliang') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.totalChargeQuantity ||
                        data?.totalChargeQuantity == 0
                            ? accountUnit(data?.totalChargeQuantity, 1000, 'h')
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('device_pingjundianya') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.avgVoltage || data.avgVoltage === 0
                            ? data.avgVoltage + ' V'
                            : '-'
                    }}
                </div>
            </div>
            <div class="descriptions-item">
                <div class="descriptions-item-label">
                    {{ $t('Total power') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.realtimePower || data?.realtimePower == 0
                            ? data?.realtimePower + ' kW'
                            : '-'
                    }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div class="descriptions-item-label">
                    {{ $t('station_leijifangdianliang') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data.totalDischargeQuantity ||
                        data.totalDischargeQuantity == 0
                            ? accountUnit(
                                  data.totalDischargeQuantity,
                                  1000,
                                  'h'
                              )
                            : '-'
                    }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { accountUnit, batteryStatus, runningStatus } from '../const'
export default {
    name: 'bmsBox',
    props: {
        objectStyle: {
            type: Object,
            default: () => {
                return {
                    height: '1000%',
                }
            },
        },
        data: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    setup(props, { emit }) {
        const showBox = () => {
            emit('showBox', true)
        }
        return {
            accountUnit,
            batteryStatus,
            runningStatus,
            showBox,
        }
    },
}
</script>

<style scoped lang="less">
.descriptions-item {
    width: 33.33%;
    display: flex;
    border-right-style: solid;
    border-right-color: rgba(34, 34, 34, 0.08);
    border-right-width: 1px;

    font-size: 14px;
    // align-items: center;
    .descriptions-item-label {
        color: var(--text-100);
        padding-left: 32px;
        width: 60%;
    }

    .descriptions-content {
        color: var(--text-100);
    }

    &:nth-child(3n) {
        border-right-color: transparent !important;
    }
}

.origin {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: rgba(51, 190, 79, 1);
    vertical-align: middle;
    border-radius: 50%;
}

.ul-box {
    height: 40px;
    line-height: 40px;
    background: var(--bg-f5);
}

.pt-6 {
    padding-top: 24px !important;
}

.text-sm {
    font-size: 14px !important;
    line-height: 20px !important;
}

.pl-8 {
    padding-left: 32px !important;
}

.ml-1 {
    margin-left: 4px !important;
}

.mb-5 {
    margin-bottom: 20px !important;
}
</style>
