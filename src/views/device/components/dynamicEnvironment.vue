<template>
    <div :style="objectStyle" class="dynamic pt-6">
        <div class="w-full flex flex-col bg">
            <div class="flex li">
                <div class="title leading-5">
                    {{ $t('device_type_yelengji') }}
                </div>
                <div class="flex-1 flex flex-wrap fontSize leading-5">
                    <!-- <div class="w-1/2 p-1"><span>工作状态：</span><span>{{ data?.workStatus }}</span></div> -->
                    <!-- <div class="w-1/2 p-1"><span>运行状态：</span><span></span></div> -->
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_huishuiwendu') }}：</span
                        ><span class="title-content">{{
                            data?.refrigerator?.waterInletTemp ||
                            data?.refrigerator?.waterInletTemp == 0
                                ? data?.refrigerator?.waterInletTemp + ' °C'
                                : '-'
                        }}</span>
                    </div>
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_chushuiwendu') }}：</span
                        ><span class="title-content">{{
                            data?.refrigerator?.waterOutletTemp ||
                            data?.refrigerator?.waterOutletTemp == 0
                                ? data?.refrigerator?.waterOutletTemp + ' °C'
                                : '-'
                        }}</span>
                    </div>
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_huishuiyali') }}：</span
                        ><span class="title-content">{{
                            data?.refrigerator?.waterInletPressure ||
                            data?.refrigerator?.waterInletPressure == 0
                                ? data?.refrigerator?.waterInletPressure +
                                  ' bar'
                                : '-'
                        }}</span>
                    </div>
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_gongshuiyali') }}：</span
                        ><span class="title-content">{{
                            data?.refrigerator?.waterOutletPressure ||
                            data?.refrigerator?.waterOutletPressure == 0
                                ? data?.refrigerator?.waterOutletPressure +
                                  ' bar'
                                : '-'
                        }}</span>
                    </div>
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_huanjingwendu') }}：</span
                        ><span class="title-content">{{
                            data?.refrigerator?.environmentTemp ||
                            data?.refrigerator?.environmentTemp == 0
                                ? data?.refrigerator?.environmentTemp + ' °C'
                                : '-'
                        }}</span>
                    </div>
                </div>
            </div>
            <div class="flex li">
                <div class="title">{{ t('station_xiaofangyangan') }}</div>
                <div class="flex-1 flex flex-wrap fontSize">
                    <!-- <div class="w-1/2 p-1"><span>在线状态：</span><span></span></div> -->
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width">{{ t('Temp') }}：</span
                        ><span class="title-content">{{
                            data?.fire?.temp || data?.fire?.temp == 0
                                ? data?.fire?.temp + ' °C'
                                : '-'
                        }}</span>
                    </div>
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex"></div>
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_yanwunongdu1') }}：</span
                        ><span class="title-content">{{
                            data?.fire?.smokeConc1 ||
                            data?.fire?.smokeConc1 == 0
                                ? data?.fire?.smokeConc1 + ' dB/M'
                                : '-'
                        }}</span>
                    </div>
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_yanwunongdu2') }}：</span
                        ><span class="title-content">{{
                            data?.fire?.smokeConc2 ||
                            data?.fire?.smokeConc2 == 0
                                ? data?.fire?.smokeConc2 + ' dB/M'
                                : '-'
                        }}</span>
                    </div>
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_conongdu') }}：</span
                        ><span class="title-content">{{
                            data?.fire?.coConc || data?.fire?.coConc == 0
                                ? data?.fire?.coConc + ' ppm'
                                : '-'
                        }}</span>
                    </div>
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_vocnongdu') }}：</span
                        ><span class="title-content">{{
                            data?.fire?.hydrogenConc ||
                            data?.fire?.hydrogenConc == 0
                                ? data?.fire?.hydrogenConc + ' ppm'
                                : '-'
                        }}</span>
                    </div>
                </div>
            </div>
            <div class="flex li">
                <div class="title">{{ t('station_chushiji') }}</div>
                <div class="flex-1 flex flex-wrap fontSize">
                    <!-- <div class="w-1/2 p-1"><span>在线状态：</span></div> -->
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_huanjingwendu') }}：</span
                        ><span class="title-content">{{
                            data?.dehumidifier?.environmentTemp ||
                            data?.dehumidifier?.environmentTemp == 0
                                ? data?.dehumidifier?.environmentTemp + ' °C'
                                : '-'
                        }}</span>
                    </div>
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_huanjingshidu') }}：</span
                        ><span class="title-content">{{
                            data?.dehumidifier?.environmentHumidity ||
                            data?.dehumidifier?.environmentHumidity == 0
                                ? data?.dehumidifier?.environmentHumidity + ' %'
                                : '-'
                        }}</span>
                    </div>
                </div>
            </div>
            <div class="flex li">
                <div class="title">{{ t('station_shuijin') }}</div>
                <div class="flex-1 flex flex-wrap fontSize">
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_shuijinkaiguan') }}：</span
                        ><span class="title-content">{{
                            data?.waterSoak?.waterSoak === 0
                                ? $t('Normal')
                                : data?.waterSoak?.waterSoak === null
                                ? '-'
                                : $t('status_yichang')
                        }}</span>
                    </div>
                </div>
            </div>
            <div class="flex li">
                <div class="title">{{ t('station_jiting') }}</div>
                <div class="flex-1 flex flex-wrap fontSize">
                    <div class="w-1/2 pr-1 pl-1 mb-1 flex">
                        <span class="title-width"
                            >{{ t('station_jitingkaiguan') }}：</span
                        ><span class="title-content">{{
                            data?.emergencyStop?.emergencyStop === 0
                                ? $t('Normal')
                                : data?.emergencyStop?.emergencyStop === null
                                ? '-'
                                : $t('status_yichang')
                        }}</span>
                    </div>
                </div>
            </div>
            <div class="flex li">
                <div class="title">{{ t('station_menjin') }}</div>
                <div class="flex-1 flex flex-wrap fontSize">
                    <div class="w-1/2 pr-1 pb-1 pl-1 flex">
                        <span class="title-width"
                            >{{ t('station_menjinkaiguan') }}：</span
                        ><span class="title-content">{{
                            data?.doorOpen?.doorOpen
                                ? $t('station_dakai')
                                : $t('common_guanbi')
                        }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { useI18n } from 'vue-i18n'
export default {
    name: 'dynamicEnvironment',
    props: {
        objectStyle: {
            type: Object,
            default: () => {
                return {
                    height: '100%',
                }
            },
        },
        data: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    setup(props) {
        const { t } = useI18n()
        return {
            t,
        }
    },
}
</script>

<style scoped lang="less">
.dynamic {
    width: 100%;
    padding: 24px 10px 10px 10px;
    // font-size: 13px;
    .bg {
        // background-color: rgba(245, 247, 247, 1);
        padding: 14px 16px;
        .li {
            & + .li {
                border-top: 1px solid var(--border);
                padding-top: 12px;
            }
        }
    }
    .title {
        width: 160px;
        text-align: left;
        padding-left: 31px;
        font-size: 14px;
        color: var(--text-100);
        font-weight: 500;
    }
    .title-width {
        display: inline-block;
        width: 172px;
        color: var(--text-60);
    }

    .title-content {
        color: var(--text-100);
    }

    .fontSize {
        font-size: 14px;
        color: var(--text-60);
    }

    .pt-6 {
        padding-top: 24px !important;
    }
}
</style>
