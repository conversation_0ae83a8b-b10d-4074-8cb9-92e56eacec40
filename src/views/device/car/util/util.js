// utils/convertTableHeaders.js
import moment from 'moment'
import { getState } from '@/common/util.js'

const formatterStatus = (t, e) => {
  return t(getState(e.chargeStatus, 'power').label)
}

const DEFAULT_WIDTH = 150

// 固定字段配置
function fixedFieldMap(t, locale) {
  return {
    time: {
      value: 'time',
      title: t('Collection time'),
      key: 'time',
      dataKey: 'time',
      width: 200,
      cellRenderer: ({ cellData: time }) =>
        moment(new Date(time * 1000)).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
      fixed: 'left',
      align: 'left'
    },

    status: {
      value: 'status',
      title: t('Charging and discharging status'),
      key: 'status',
      dataKey: 'status',
      width: locale == 'en' ? 240 : 150,
      cellRenderer: ({ cellData: status }) => {
        return formatterStatus(t, { chargeStatus: status })
      },
      align: 'center',
    },

    alarmSta: {
      value: 'alarmSta',
      title: t('Alarm status'),
      key: 'alarmSta',
      dataKey: 'alarmSta',
      width: 120,
      cellRenderer: ({ cellData: alarmSta }) => {
        return alarmSta == 1 ? ' 异常' : '正常'
      },
      align: 'center',
    },

    lockSta: {
      value: 'lockSta',
      title: t('Lock status'),
      key: 'lockSta',
      dataKey: 'lockSta',
      width: 150,
      cellRenderer: ({ cellData: lockSta }) => {
        return lockSta === 0
          ? t('Normal')
          : lockSta === 1 ? t('locked') : ''
      }, // 1:锁住 0:未锁
      align: 'center',
    },
  }
}

// 将后端返回的表头数据转换成 el-table 组件需要的表头格式
export function convertTableHeaders(data, t, locale) {
  const fixedFields = fixedFieldMap(t, locale)
  const columns = []

  // 遍历后端返回的表头数据
  Object.entries(data).forEach(([key, title]) => {
    // 检查是否是需要特殊处理的字段
    if (fixedFields[key]) {
      // 使用特殊配置，但保持后端返回的标题
      columns.push({
        ...fixedFields[key],
        label: title, // el-table 使用 label 作为表头显示文本
        prop: key,    // el-table 使用 prop 作为数据字段
      })
    } else {
      // 普通字段，使用默认配置
      columns.push({
        value: key,
        dataKey: key,
        key: key,
        title: title,
        width: DEFAULT_WIDTH,
        align: 'center',
      })
    }
  })

  return columns
}

export function roundUp(n) {
  if (n <= 0) return 0
  const digits = Math.floor(Math.log10(n)) + 1
  const base = Math.pow(10, digits - 1)
  const result = Math.ceil(n / base) * base
  return result
}