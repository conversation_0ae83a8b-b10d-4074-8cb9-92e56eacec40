<template>
    <div class="flex-1 pr-6 w-0 pt-4">
        <div class="flex justify-between items-center mb-3">
            <div class="flex flex-1 items-center gap-x-5">
                <div
                    class="overflow text-2xl font-medium h-6 leading-6 flex-1 w-0 text-title dark:text-title-dark"
                    v-if="projectData?.projectName"
                >
                    {{ projectData?.projectName || '-' }}
                </div>
                <div
                    class="overflow text-2xl font-medium bg-ff dark:bg-ff-dark h-8 w-40"
                    v-else
                ></div>
            </div>
        </div>
        <div class="flex items-center mb-4 justify-between">
            <div
                class="text-xs text-secondar-text dark:text-60-dark leading-6 w-0 flex-1 overflow-hidden overflow-ellipsis whitespace-nowrap"
                v-if="customerDetail.projectId"
            >
                {{ $t('No') }}：
                <el-tooltip
                    v-if="projectData?.projectNo"
                    class="box-item"
                    :content="projectData?.projectNo"
                    placement="top-start"
                >
                    <span class="tag cursor-pointer w-0">{{
                        projectData?.projectNo
                    }}</span>
                </el-tooltip>
            </div>
            <div class="flex items-center leading-6 gap-x-2">
                <div
                    class="flex items-center gap-x-1 px-2 rounded-sm"
                    style="color: #2bdbff; background: rgba(50, 220, 255, 0.2)"
                >
                    <div class="text-xs leading-5.5">
                        {{ $t('Total devices') }}:
                        {{ projectData?.bmsSummaryInfo?.totalDevices || '0'
                        }}{{ $t('tai') }}
                    </div>
                </div>
                <div
                    class="flex items-center gap-x-1 px-2 rounded-sm"
                    style="color: #fd750b; background: rgba(253, 117, 11, 0.1)"
                >
                    <div class="text-xs leading-5.5">
                        {{ $t('Activated1') }}:
                        {{ projectData?.bmsSummaryInfo?.activeCount || '0'
                        }}{{ $t('tai') }}
                    </div>
                </div>
                <div
                    class="flex items-center gap-x-1 px-2 rounded-sm"
                    style="color: #3edacd; background: rgba(62, 218, 205, 0.1)"
                >
                    <div class="text-xs leading-5.5">
                        {{ $t('Online') }}:
                        {{ projectData?.bmsSummaryInfo?.onlineCount || '0'
                        }}{{ $t('tai') }}
                    </div>
                </div>
            </div>
        </div>
        <div
            class="inline-flex justify-between items-center gap-x-10 mb-3"
            v-if="customerDetail.projectId"
        >
            <div class="">
                <div
                    class="text-secondar-text dark:text-60-dark leading-4 mb-2"
                >
                    {{ $t('Vehicle type') }}
                </div>
                <div
                    class="text-base font-medium leading-4 text-title dark:text-title-dark"
                >
                    {{ projectData?.vehicleTypeLabel || '-' }}
                </div>
            </div>
            <div class="w-0.5 h-10 bg-border"></div>
            <div class="text-center">
                <div class="inline-block text-left">
                    <div
                        class="text-secondar-text dark:text-60-dark leading-4 mb-2"
                    >
                        {{ $t('Cell type') }}
                    </div>
                    <div
                        class="text-base font-medium leading-4 text-title dark:text-title-dark"
                    >
                        {{ projectData?.cellTypeLabel || '-' }}
                    </div>
                </div>
            </div>
            <div class="w-0.5 h-10 bg-border"></div>
            <div class="text-center">
                <div class="inline-block text-left">
                    <div
                        class="text-secondar-text dark:text-60-dark leading-4 mb-2"
                    >
                        {{ $t('Power type') }}
                    </div>
                    <div
                        class="text-base font-medium leading-4 text-title dark:text-title-dark"
                    >
                        {{ projectData?.powerTypeLabel || '-' }}
                    </div>
                </div>
            </div>
        </div>
        <div
            class="inline-flex justify-between items-center gap-x-10"
            :class="[
                // locale == 'en' ? 'gap-x-5' : 'gap-x-10',
                customerDetail.projectId ? ' mb-3' : 'my-8',
            ]"
        >
            <div class="" v-if="customerDetail.projectId">
                <div
                    class="text-secondar-text dark:text-60-dark leading-4 mb-2"
                >
                    {{ $t('Device model') }}
                </div>
                <div
                    class="text-base font-medium leading-4 text-title dark:text-title-dark"
                >
                    <template v-if="projectData?.model?.length < 10">
                        <span class="dark:text-title-dark">
                            {{ projectData?.model || '-' }}
                        </span>
                    </template>
                    <el-tooltip
                        v-else-if="projectData?.model"
                        class="box-item"
                        :effect="isDark ? 'dark' : 'light'"
                        :content="projectData?.model"
                        placement="top-start"
                    >
                        <span
                            class="dark:text-title-dark inline-block w-28 overflow-hidden overflow-ellipsis whitespace-nowrap"
                        >
                            {{ projectData?.model || '-' }}
                        </span>
                    </el-tooltip>
                </div>
            </div>
            <div
                class="w-0.5 h-10 bg-border"
                v-if="customerDetail.projectId"
            ></div>
            <div class="text-center">
                <div class="inline-block text-left">
                    <div
                        class="text-secondar-text dark:text-60-dark leading-4 mb-2"
                    >
                        {{ $t('Loading Energy') }}
                    </div>
                    <div
                        class="text-base font-medium leading-4 text-title dark:text-title-dark"
                    >
                        {{
                            unitConversion(
                                projectData?.bmsSummaryInfo?.totalEnergy || 0,
                                1000
                            )
                        }}
                        <span class="text-xs">{{
                            alternateUnits(
                                projectData?.bmsSummaryInfo?.totalEnergy || 0,
                                1000
                            )
                                ? 'MWh'
                                : 'kWh'
                        }}</span>
                    </div>
                </div>
            </div>
            <div class="w-0.5 h-10 bg-border"></div>
            <div class="text-center">
                <div class="inline-block text-left">
                    <div
                        class="text-secondar-text dark:text-60-dark leading-4 mb-2"
                    >
                        {{ $t('runTime') }}
                    </div>
                    <div
                        class="text-base font-medium leading-4 text-title dark:text-title-dark"
                    >
                        {{ projectData?.bmsSummaryInfo?.dsgTimeSum || '0' }}
                        <span class="text-xs">h</span>
                    </div>
                </div>
            </div>
            <div
                class="w-0.5 h-10 bg-border"
                v-if="!customerDetail.projectId"
            ></div>
            <div class="text-center" v-if="!customerDetail.projectId">
                <div class="inline-block text-left">
                    <div
                        class="text-secondar-text dark:text-60-dark leading-4 mb-2"
                    >
                        {{
                            customerDetail.projectId
                                ? $t('Initiation date')
                                : $t('station_touyunriqi')
                        }}
                    </div>
                    <div
                        class="text-base font-medium leading-4 text-title dark:text-title-dark"
                    >
                        {{ projectData?.createDate || '-' }}
                    </div>
                </div>
            </div>
        </div>

        <div class="mb-3.5">
            <div
                class="px-2 py-2 bg-f5f7f7 dark:bg-ffffff-dark rounded text-secondar-text dark:text-60-dark opacity-80 overflow"
            >
                <div
                    class="overflow float-left"
                    style="max-width: 50%; padding-right: 2%; line-height: 14px"
                    :title="projectData?.majorCustomers"
                    v-if="customerDetail.projectId"
                >
                    {{ $t('Main customer') }}：{{
                        projectData?.majorCustomers || '-'
                    }}
                </div>
                <div
                    class="overflow float-left"
                    style="max-width: 50%; padding-right: 2%; line-height: 14px"
                    :title="projectData?.supplierName"
                    v-else
                >
                    {{ $t('station_fuwushang') }}：{{
                        projectData?.supplierName || '-'
                    }}
                </div>
                <div
                    v-if="customerDetail.projectId"
                    class="overflow float-left"
                    style="
                        max-width: 50%;
                        padding-left: 2%;
                        border-left: 1px solid #d9d9d9;
                        line-height: 14px;
                    "
                    :title="projectData?.mainCitys"
                >
                    {{ $t('Location') }}：{{
                        projectData?.mainCitys?.length
                            ? projectData?.mainCitys.join('、')
                            : '-'
                    }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import Decimal from 'decimal.js'
import { unitConversion, alternateUnits } from '../../const'

const { t, locale } = useI18n()
const store = useStore()

const props = defineProps({
    projectData: {
        type: Object,
        default: () => ({}),
    },
    customerDetail: {
        type: Object,
        default: () => ({}),
    },
})

const isDark = computed(() => {
    return store.state.theme.isDark
})

const totalCapacity = computed(() => {
    if (!props.projectData?.bmsSummaryInfo?.totalDevices) return 0
    if (!props.projectData?.ratedPower) return 0
    return new Decimal(props.projectData?.bmsSummaryInfo?.totalDevices).mul(
        new Decimal(props.projectData?.ratedPower)
    )
})
</script>

<style scoped lang="less">
.tag {
    // color: var(--themeColor);
}
</style>
