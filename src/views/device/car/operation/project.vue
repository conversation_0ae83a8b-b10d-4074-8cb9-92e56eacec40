<template>
    <div class="custom-table flex flex-col">
        <!-- 列设置按钮 -->
        <div class="table-header flex justify-end gap-x-3">
            <div class="flex items-center">
                <el-input v-model="searchValueP" class="">
                    <template #prepend>
                        <el-select
                            v-model="searchTypeP"
                            placeholder="请选择"
                            style="width: 112px"
                            @change="onChangeSearchTypeP"
                        >
                            <el-option
                                v-for="item in searchTypesP"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </template>
                    <template #append>
                        <el-button
                            plain
                            :icon="Search"
                            @click="debouncedOnSearchP"
                        />
                    </template>
                </el-input>
            </div>
            <el-button @click="showColumnSelector" plain round>
                {{ $t('Custom Columns') }}
                <iconSvg name="customList" :class="'w-4 h-4 ml-1'" />
            </el-button>
            <el-button @click="addProject" plain round>
                {{ $t('Add project')
                }}<iconSvg name="addDevice" :class="'w-4 h-4 ml-1'" />
            </el-button>
        </div>
        <div
            class="flex-1 h-0 overflow-y-auto mt-5 table-box"
            v-loading="tableLoading"
        >
            <!-- 表格主体 -->
            <div class="w-full h-full">
                <el-table
                    :data="tableData"
                    :height="'100%'"
                    @row-click="rowClick"
                    @sort-change="handleSortChange"
                    style="width: 100%"
                    :empty-text="$t('zanwushuju')"
                >
                    <!-- 动态渲染列 -->
                    <template v-for="col in visibleColumns" :key="col.key">
                        <!-- 项目名称列 -->
                        <el-table-column
                            v-if="col.key === 'projectName'"
                            prop="projectName"
                            :label="$t('Project name')"
                            width="280"
                            fixed="left"
                        >
                            <template #default="{ row }">
                                <div class="flex flex-col">
                                    <div class="text-100">
                                        {{ row.projectName }}
                                    </div>
                                    <div class="text-80">
                                        {{ $t('Project No') }}：{{
                                            row.projectNo
                                        }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>

                        <!-- 主要客户列 -->
                        <el-table-column
                            v-else-if="col.key === 'majorCustomers'"
                            prop="majorCustomers"
                            :label="$t('Main customer')"
                            width="140"
                        />

                        <!-- 总设备数列 -->
                        <el-table-column
                            v-else-if="col.key === 'totalDevices'"
                            prop="totalDevices"
                            :label="$t('Total devices')"
                            :width="locale == 'en' ? '140' : '108'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                        />

                        <!-- 激活设备数列 -->
                        <el-table-column
                            v-else-if="col.key === 'activeCount'"
                            prop="activeCount"
                            :label="$t('Activated devices')"
                            :width="locale == 'en' ? '172' : '124'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                        />

                        <!-- 在线设备数列 -->
                        <el-table-column
                            v-else-if="col.key === 'onlineCount'"
                            prop="onlineCount"
                            :label="$t('Online devices')"
                            :width="locale == 'en' ? '164' : '124'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                        />

                        <!-- 设备型号列 -->
                        <el-table-column
                            v-else-if="col.key === 'model'"
                            prop="model"
                            :label="$t('Device model')"
                            width="144"
                        />

                        <!-- 车辆类型列 -->
                        <el-table-column
                            v-else-if="col.key === 'vehicleType'"
                            prop="vehicleType"
                            :label="$t('Vehicle type')"
                            :width="locale == 'en' ? '144' : '124'"
                        >
                            <template #header>
                                <div class="flex items-center">
                                    <span class="mr-2 text-sm">{{
                                        $t('Vehicle type')
                                    }}</span>
                                    <el-popover
                                        trigger="click"
                                        width="200"
                                        v-model:visible="
                                            vehicleTypeFilterVisible
                                        "
                                    >
                                        <template #reference>
                                            <div
                                                class="cursor-pointer leading-4 filter-column"
                                                :class="
                                                    vehicleTypeFilter?.length
                                                        ? 'hasFilter'
                                                        : ''
                                                "
                                                style="margin-top: 3px"
                                                data-vehicle-type-filter
                                                tabindex="0"
                                                role="button"
                                                @keydown.enter="
                                                    vehicleTypeFilterVisible =
                                                        !vehicleTypeFilterVisible
                                                "
                                                @keydown.space.prevent="
                                                    vehicleTypeFilterVisible =
                                                        !vehicleTypeFilterVisible
                                                "
                                            >
                                                <iconSvg
                                                    name="filter"
                                                    class="w-3.5 h-3.5"
                                                />
                                            </div>
                                        </template>
                                        <div class="filter-wrapper">
                                            <div class="filter-options">
                                                <div class="overflow-y-auto">
                                                    <el-checkbox
                                                        v-for="item in vehicleTypeOptions"
                                                        :key="item.value"
                                                        :model-value="
                                                            selectedVehicleTypes.includes(
                                                                item.value
                                                            )
                                                        "
                                                        @change="
                                                            (val) =>
                                                                onVehicleTypeFilterChange(
                                                                    item.value,
                                                                    val
                                                                )
                                                        "
                                                    >
                                                        {{ item.label }}
                                                    </el-checkbox>
                                                </div>
                                            </div>
                                            <div
                                                class="flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008"
                                            >
                                                <el-button
                                                    round
                                                    @click="
                                                        onVehicleTypeFilterReset
                                                    "
                                                    >{{
                                                        $t('reset')
                                                    }}</el-button
                                                >
                                                <el-button
                                                    type="primary"
                                                    plain
                                                    round
                                                    @click="
                                                        onVehicleTypeFilterConfirm
                                                    "
                                                    >{{
                                                        $t('Confirm')
                                                    }}</el-button
                                                >
                                            </div>
                                        </div>
                                    </el-popover>
                                </div>
                            </template>
                            <template #default="{ row }">
                                {{ getVehicleType(row.vehicleType) }}
                            </template>
                        </el-table-column>

                        <!-- 车辆型号列 -->
                        <el-table-column
                            v-else-if="col.key === 'vehicleModel'"
                            prop="vehicleModel"
                            :label="$t('Vehicle model')"
                            :width="locale == 'en' ? '144' : '124'"
                        >
                            <template #default="{ row }">
                                {{ getVehicleModel(row.vehicleModel) }}
                            </template>
                        </el-table-column>

                        <!-- 电池类型列 -->
                        <el-table-column
                            v-else-if="col.key === 'cellType'"
                            prop="cellType"
                            :label="$t('Cell type')"
                            width="144"
                        >
                            <template #header>
                                <div class="flex items-center">
                                    <span class="mr-2 text-sm">{{
                                        $t('Cell type')
                                    }}</span>
                                    <el-popover
                                        trigger="click"
                                        width="200"
                                        v-model:visible="cellTypeFilterVisible"
                                    >
                                        <template #reference>
                                            <div
                                                class="cursor-pointer leading-4 filter-column"
                                                :class="
                                                    cellTypeFilter?.length
                                                        ? 'hasFilter'
                                                        : ''
                                                "
                                                style="margin-top: 3px"
                                                data-cell-type-filter
                                                tabindex="0"
                                                role="button"
                                                @keydown.enter="
                                                    cellTypeFilterVisible =
                                                        !cellTypeFilterVisible
                                                "
                                                @keydown.space.prevent="
                                                    cellTypeFilterVisible =
                                                        !cellTypeFilterVisible
                                                "
                                            >
                                                <iconSvg
                                                    name="filter"
                                                    class="w-3.5 h-3.5"
                                                />
                                            </div>
                                        </template>
                                        <div class="filter-wrapper">
                                            <div class="filter-options">
                                                <div class="overflow-y-auto">
                                                    <el-checkbox
                                                        v-for="item in cellTypeOptions"
                                                        :key="item.value"
                                                        :model-value="
                                                            selectedCellTypes.includes(
                                                                item.value
                                                            )
                                                        "
                                                        @change="
                                                            (val) =>
                                                                onCellTypeFilterChange(
                                                                    item.value,
                                                                    val
                                                                )
                                                        "
                                                    >
                                                        {{ item.label }}
                                                    </el-checkbox>
                                                </div>
                                            </div>
                                            <div
                                                class="flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008"
                                            >
                                                <el-button
                                                    round
                                                    @click="
                                                        onCellTypeFilterReset
                                                    "
                                                    >{{
                                                        $t('reset')
                                                    }}</el-button
                                                >
                                                <el-button
                                                    type="primary"
                                                    plain
                                                    round
                                                    @click="
                                                        onCellTypeFilterConfirm
                                                    "
                                                    >{{
                                                        $t('Confirm')
                                                    }}</el-button
                                                >
                                            </div>
                                        </div>
                                    </el-popover>
                                </div>
                            </template>
                        </el-table-column>

                        <!-- 电源类型列 -->
                        <el-table-column
                            v-else-if="col.key === 'powerType'"
                            prop="powerType"
                            :label="$t('Power type')"
                            width="144"
                        >
                            <template #header>
                                <div class="flex items-center">
                                    <span class="mr-2 text-sm">{{
                                        $t('Power type')
                                    }}</span>
                                    <el-popover
                                        trigger="click"
                                        width="200"
                                        v-model:visible="powerTypeFilterVisible"
                                    >
                                        <template #reference>
                                            <div
                                                class="cursor-pointer leading-4 filter-column"
                                                :class="
                                                    powerTypeFilter?.length
                                                        ? 'hasFilter'
                                                        : ''
                                                "
                                                style="margin-top: 3px"
                                                data-power-type-filter
                                                tabindex="0"
                                                role="button"
                                                @keydown.enter="
                                                    powerTypeFilterVisible =
                                                        !powerTypeFilterVisible
                                                "
                                                @keydown.space.prevent="
                                                    powerTypeFilterVisible =
                                                        !powerTypeFilterVisible
                                                "
                                            >
                                                <iconSvg
                                                    name="filter"
                                                    class="w-3.5 h-3.5"
                                                />
                                            </div>
                                        </template>
                                        <div class="filter-wrapper">
                                            <div class="filter-options">
                                                <div class="overflow-y-auto">
                                                    <el-checkbox
                                                        v-for="item in powerTypeOptions"
                                                        :key="item.value"
                                                        :model-value="
                                                            selectedPowerTypes.includes(
                                                                item.value
                                                            )
                                                        "
                                                        @change="
                                                            (val) =>
                                                                onPowerTypeFilterChange(
                                                                    item.value,
                                                                    val
                                                                )
                                                        "
                                                    >
                                                        {{ item.label }}
                                                    </el-checkbox>
                                                </div>
                                            </div>
                                            <div
                                                class="flex justify-end gap-2 pt-2 border-t border-border dark:border-border-dark-008"
                                            >
                                                <el-button
                                                    round
                                                    @click="
                                                        onPowerTypeFilterReset
                                                    "
                                                    >{{
                                                        $t('reset')
                                                    }}</el-button
                                                >
                                                <el-button
                                                    type="primary"
                                                    plain
                                                    round
                                                    @click="
                                                        onPowerTypeFilterConfirm
                                                    "
                                                    >{{
                                                        $t('Confirm')
                                                    }}</el-button
                                                >
                                            </div>
                                        </div>
                                    </el-popover>
                                </div>
                            </template>
                        </el-table-column>

                        <!-- 地区列 -->
                        <el-table-column
                            v-else-if="col.key === 'city'"
                            prop="city"
                            :label="$t('Region')"
                            width="124"
                        />

                        <!-- 总容量列 -->
                        <el-table-column
                            v-else-if="col.key === 'totalCapacity'"
                            prop="totalCapacity"
                            :label="$t('Total capacity') + '(Ah)'"
                            :width="locale == 'en' ? '156' : '124'"
                        />

                        <!-- 总能量列 -->
                        <el-table-column
                            v-else-if="col.key === 'totalEnergy'"
                            prop="totalEnergy"
                            :label="$t('TotalEnergy') + '(kWh)'"
                            :width="locale == 'en' ? '156' : '124'"
                        />

                        <!-- 总功率列 -->
                        <el-table-column
                            v-else-if="col.key === 'ratedPower'"
                            prop="ratedPower"
                            :label="$t('Total power') + '(Ah)'"
                            :width="locale == 'en' ? '156' : '124'"
                        />

                        <!-- 充电时长列 -->
                        <el-table-column
                            v-else-if="col.key === 'chgTimeSum'"
                            prop="chgTimeSum"
                            :label="$t('Charging duration') + '(h)'"
                            :width="locale == 'en' ? '180' : '124'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                        />

                        <!-- 充电量列 -->
                        <el-table-column
                            v-else-if="col.key === 'chgCapSum'"
                            prop="chgCapSum"
                            :label="$t('Charging amount') + '(Ah)'"
                            :width="locale == 'en' ? '188' : '124'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                        />

                        <!-- 放电时长列 -->
                        <el-table-column
                            v-else-if="col.key === 'dsgTimeSum'"
                            prop="dsgTimeSum"
                            :label="$t('Discharging duration') + '(h)'"
                            :width="locale == 'en' ? '208' : '124'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                        />

                        <!-- 放电量列 -->
                        <el-table-column
                            v-else-if="col.key === 'dsgCapSum'"
                            prop="dsgCapSum"
                            :label="$t('Discharging amount') + '(Ah)'"
                            :width="locale == 'en' ? '216' : '124'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                        />

                        <!-- 告警数列 -->
                        <el-table-column
                            v-else-if="col.key === 'totalAlarms'"
                            prop="totalAlarms"
                            :label="$t('Alarm count')"
                            :width="locale == 'en' ? '140' : '124'"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                        />

                        <!-- 创建日期列 -->
                        <el-table-column
                            v-else-if="col.key === 'createDate'"
                            prop="createDate"
                            :label="$t('Initiation date')"
                            width="144"
                            sortable="custom"
                            :sort-orders="['ascending', 'descending']"
                        />

                        <!-- 操作列 -->
                        <el-table-column
                            v-else-if="col.key === 'Operation'"
                            :label="$t('Operation')"
                            :width="locale == 'en' ? '100' : '88'"
                            align="center"
                            fixed="right"
                        >
                            <template #default="{ row }">
                                <div
                                    class="flex items-center justify-center mx-auto"
                                >
                                    <el-dropdown
                                        trigger="hover"
                                        @command="
                                            (command) =>
                                                handleOperation(command, row)
                                        "
                                        class="operation-drop"
                                    >
                                        <div
                                            class="cursor-pointer outline-none border-none mx-auto w-10 h-10 leading-10 text-center operation-icon"
                                            style="margin-top: 3px"
                                            @click.stop
                                        >
                                            <iconSvg
                                                name="arrowdrop"
                                                class="w-3.5 h-3.5 text-80 dark:text-80-dark"
                                            />
                                        </div>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <el-dropdown-item
                                                    command="viewConfig"
                                                >
                                                    {{
                                                        $t('View Configuration')
                                                    }}
                                                    <el-icon class="ml-2">
                                                        <View />
                                                    </el-icon>
                                                </el-dropdown-item>
                                                <el-dropdown-item
                                                    command="addDevice"
                                                >
                                                    {{ $t('add_device') }}
                                                    <el-icon class="ml-2">
                                                        <Plus />
                                                    </el-icon>
                                                </el-dropdown-item>
                                                <el-dropdown-item
                                                    command="deleteRecord"
                                                    :disabled="
                                                        row.totalDevices > 0
                                                    "
                                                >
                                                    {{ $t('Deleting Records') }}
                                                    <el-icon class="ml-2">
                                                        <Delete />
                                                    </el-icon>
                                                </el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </div>
                            </template>
                        </el-table-column>
                    </template>
                </el-table>
            </div>
        </div>
        <div class="flex justify-center mt-4">
            <el-pagination
                background
                layout="prev, pager, next"
                :total="projectPageTotal"
                v-model:current-page="projectPageInfo.current"
                :page-size="projectPageInfo.size"
                @change="debouncedProjectPageChange"
                @size-change="handleProjectSizeChange"
            />
        </div>

        <!-- 列选择器弹窗 -->
        <el-drawer
            v-model="dialogVisible"
            :size="500"
            :lockScroll="true"
            :show-close="false"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div class="drawer-header text-title dark:text-title-dark">
                        <span>{{ $t('column_settings') }}</span>
                    </div>
                    <div class="flex gap-x-3">
                        <el-button plain round @click="closeDrawer">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <el-button plain round @click="onSave" type="primary">{{
                            $t('Confirm')
                        }}</el-button>
                    </div>
                </div>
            </template>
            <div class="flex items-start h-full">
                <div class="flex-1">
                    <div class="flex justify-start items-center">
                        <el-checkbox
                            v-model="checkAll"
                            :indeterminate="isIndeterminate"
                            @change="handleCheckAllChange"
                            style="width: auto"
                        >
                            {{ $t('Select all') }}
                            {{
                                checkedColumns.length
                                    ? '(' + (checkedColumns.length - 1) + ')'
                                    : ''
                            }}
                        </el-checkbox>
                        <!-- <el-button
                            type="text"
                            size="small"
                            @click="resetToDefault"
                            class="ml-4"
                        >
                            {{ $t('reset') }}
                        </el-button>
                        <div
                            class="ml-4 text-title dark:text-title-dark cursor-pointer select-none"
                            @click="onInvert"
                        >
                            反选
                        </div> -->
                    </div>
                    <el-checkbox-group
                        v-model="checkedColumns"
                        @change="onChangeCheckedColumns"
                        class="column-selector"
                    >
                        <el-checkbox
                            v-for="(item, index) in columnList"
                            :value="item.key"
                            :key="item.key"
                            :disabled="index < 2 || item.key === 'Operation'"
                            class="pl-4 relative"
                            v-show="item.key !== 'Operation'"
                        >
                            <div
                                class="absolute w-2.5 h-2.5 border-l border-b left-0 rounded-bl-sm opacity-60"
                            ></div>
                            {{ item.title }}
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
                <el-divider direction="vertical" style="height: 100%" />
                <div class="flex-1">
                    <draggable
                        v-model="draggabledColumns"
                        item-key="key"
                        :disabled="false"
                        handle=".drag-handle"
                        @end="handleDragEnd"
                        :move="checkMove"
                    >
                        <template #item="{ element, index }">
                            <div
                                class="column-item"
                                v-show="element.key !== 'Operation'"
                            >
                                <el-icon
                                    class="drag-handle-disabled"
                                    v-if="index < 2"
                                >
                                    <iconSvg name="lock" class="w-5 h-5" />
                                </el-icon>
                                <el-icon class="drag-handle" v-else>
                                    <iconSvg name="drag" class="w-5 h-5" />
                                </el-icon>
                                <div
                                    class="text-title dark:text-title-dark flex-1"
                                    :class="
                                        index < 2
                                            ? ' opacity-40 select-none cursor-not-allowed'
                                            : ''
                                    "
                                >
                                    {{ element.title }}
                                </div>
                                <div
                                    class="w-6 h-6 flex items-center justify-center text-title dark:text-title-dark"
                                    :class="
                                        index < 2
                                            ? ' opacity-60 cursor-not-allowed'
                                            : 'cursor-pointer'
                                    "
                                    @click="onDeleteItem(element)"
                                >
                                    <el-icon size="14">
                                        <CloseBold />
                                    </el-icon>
                                </div>
                            </div>
                        </template>
                    </draggable>
                </div>
            </div>
        </el-drawer>
        <el-drawer
            v-model="addVisible"
            :size="486"
            :show-close="false"
            @close="cancelAdd"
            :close-on-click-modal="false"
            wrapClassName="drawerBox"
            ref="projectForm"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ drawerTitle }}</span>
                    </div>
                    <!-- View Mode Buttons -->
                    <div
                        v-if="formMode === 'view'"
                        class="flex gap-x-3 items-center"
                    >
                        <el-button plain round @click="cancelAdd">{{
                            $t('common_guanbi')
                        }}</el-button>
                        <el-button plain type="primary" round @click="onEdit">{{
                            $t('common_bianji')
                        }}</el-button>
                    </div>

                    <!-- Add/Edit Mode Buttons -->
                    <div
                        v-if="formMode === 'add' || formMode === 'edit'"
                        class="flex gap-x-3 items-center"
                    >
                        <el-button plain round @click="cancelAdd">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="confirmProject()"
                            >{{
                                formMode === 'edit' ? $t('Save') : $t('Confirm')
                            }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div class="forms" id="project-forms">
                <el-form
                    ref="formRef"
                    :model="formState"
                    :rules="rules"
                    :label-width="labelWidth"
                    label-position="left"
                    hide-required-asterisk
                >
                    <el-form-item
                        :label="$t('Project name')"
                        prop="projectName"
                    >
                        <el-input
                            v-model="formState.projectName"
                            :placeholder="$t('Project name')"
                            :disabled="formMode === 'view'"
                        />
                    </el-form-item>
                    <el-form-item :label="$t('Project No')" prop="projectNo">
                        <el-input
                            v-model="formState.projectNo"
                            :placeholder="$t('Project No')"
                            :maxlength="20"
                            :disabled="formMode !== 'add'"
                            @input="handleProjectNoChange"
                        />
                    </el-form-item>
                    <div class="text-secondar-text dark:text-60-dark mb-3">
                        {{ $t('Project information configuration') }}
                    </div>
                    <div class="p-4 rounded-lg bg-f5 dark:bg-ffffff-dark">
                        <el-form-item :label="$t('Device model')" prop="model">
                            <el-select
                                v-model="formState.model"
                                :placeholder="$t('Device model')"
                                class="w-full"
                                :disabled="
                                    formMode === 'view' ||
                                    (formMode === 'edit' &&
                                        currentProject?.totalDevices > 0)
                                "
                            >
                                <el-option
                                    v-for="item in modelOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                                <template #header>
                                    <el-input
                                        v-model="modelOptionsSearchValue"
                                        style="width: 100%"
                                        :placeholder="
                                            $t('Search or create a new option')
                                        "
                                        @input="onSearchM"
                                        :disabled="
                                            formMode === 'view' ||
                                            (formMode === 'edit' &&
                                                currentProject?.totalDevices >
                                                    0)
                                        "
                                    />
                                </template>
                                <template
                                    #footer
                                    v-if="modelOptionsSearchValue.length > 0"
                                >
                                    <div
                                        class="pl-2.5 cursor-pointer select-none text-primary-text dark:text-80-dark"
                                        @click="addNewOption"
                                    >
                                        {{ $t('Create new options') }}：{{
                                            modelOptionsSearchValue
                                        }}
                                    </div>
                                </template>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            :label="$t('Vehicle type')"
                            prop="vehicleType"
                        >
                            <el-select
                                v-model="formState.vehicleType"
                                :placeholder="$t('Vehicle type')"
                                class="w-full"
                                :disabled="
                                    formMode === 'view' ||
                                    (formMode === 'edit' &&
                                        currentProject?.totalDevices > 0)
                                "
                                @change="onVehicleTypeChange"
                            >
                                <el-option
                                    v-for="item in vehicleTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            :label="$t('Vehicle model')"
                            prop="vehicleModel"
                        >
                            <el-select
                                v-model="formState.vehicleModel"
                                :placeholder="$t('Vehicle model')"
                                class="w-full"
                                :disabled="
                                    formMode === 'view' ||
                                    (formMode === 'edit' &&
                                        currentProject?.totalDevices > 0)
                                "
                                filterable
                                allow-create
                                default-first-option
                            >
                                <el-option
                                    v-for="item in filteredVehicleModelOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                                <template #header>
                                    <el-input
                                        v-model="vehicleModelSearchValue"
                                        style="width: 100%"
                                        :placeholder="
                                            $t('Search or create a new option')
                                        "
                                        @input="onSearchVehicleModel"
                                        :disabled="
                                            formMode === 'view' ||
                                            (formMode === 'edit' &&
                                                currentProject?.totalDevices >
                                                    0)
                                        "
                                    />
                                </template>
                                <template
                                    #footer
                                    v-if="vehicleModelSearchValue.length > 0"
                                >
                                    <div
                                        class="pl-2.5 cursor-pointer select-none text-primary-text dark:text-80-dark"
                                        @click="addNewVehicleModelOption"
                                    >
                                        {{ $t('Create new options') }}：{{
                                            vehicleModelSearchValue
                                        }}
                                    </div>
                                </template>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            :label="$t('Power type')"
                            prop="powerType"
                        >
                            <el-select
                                v-model="formState.powerType"
                                :placeholder="$t('Power type')"
                                class="w-full"
                                :disabled="
                                    formMode === 'view' ||
                                    (formMode === 'edit' &&
                                        currentProject?.totalDevices > 0)
                                "
                            >
                                <el-option
                                    v-for="item in fixedPowerTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('Cell type')" prop="cellType">
                            <el-select
                                v-model="formState.cellType"
                                :placeholder="$t('Cell type')"
                                class="w-full"
                                :disabled="
                                    formMode === 'view' ||
                                    (formMode === 'edit' &&
                                        currentProject?.totalDevices > 0)
                                "
                            >
                                <el-option
                                    v-for="item in fixedCellTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            :label="$t('CellPackaging')"
                            prop="cellPack"
                        >
                            <el-select
                                v-model="formState.cellPack"
                                :placeholder="$t('CellPackaging')"
                                class="w-full"
                                :disabled="
                                    formMode === 'view' ||
                                    (formMode === 'edit' &&
                                        currentProject?.totalDevices > 0)
                                "
                            >
                                <el-option
                                    v-for="item in fixedCellPackOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            :label="$t('Battery series count')"
                            prop="series"
                        >
                            <el-input-number
                                controls-position="right"
                                v-model="formState.series"
                                :min="1"
                                :max="50"
                                :precision="0"
                                class="w-full"
                                :disabled="
                                    formMode === 'view' ||
                                    (formMode === 'edit' &&
                                        currentProject?.totalDevices > 0)
                                "
                            />
                        </el-form-item>
                        <el-form-item
                            :label="$t('Battery parallel count')"
                            prop="parallels"
                        >
                            <el-input-number
                                controls-position="right"
                                v-model="formState.parallels"
                                :min="1"
                                :max="10"
                                :precision="0"
                                class="w-full"
                                :disabled="
                                    formMode === 'view' ||
                                    (formMode === 'edit' &&
                                        currentProject?.totalDevices > 0)
                                "
                                @change="calculateDerivedValues"
                            />
                        </el-form-item>
                        <el-form-item
                            :label="$t('Battery box count')"
                            prop="tanks"
                        >
                            <el-input-number
                                controls-position="right"
                                v-model="formState.tanks"
                                :min="1"
                                :max="10"
                                :precision="0"
                                class="w-full"
                                :disabled="
                                    formMode === 'view' ||
                                    (formMode === 'edit' &&
                                        currentProject?.totalDevices > 0)
                                "
                                @change="calculateDerivedValues"
                            />
                        </el-form-item>
                        <el-form-item
                            :label="$t('Individual capacity')"
                            prop="cellCapacity"
                        >
                            <div
                                class="w-full relative flex-1 flex items-center"
                            >
                                <el-input-number
                                    controls-position="right"
                                    v-model="formState.cellCapacity"
                                    :min="1"
                                    :max="1000"
                                    :precision="0"
                                    class="w-full"
                                    :disabled="
                                        formMode === 'view' ||
                                        (formMode === 'edit' &&
                                            currentProject?.totalDevices > 0)
                                    "
                                    @change="calculateDerivedValues"
                                />
                                <div
                                    class="absolute right-10 top-0.5 leading-8 text-secondar-text dark:text-60-dark"
                                >
                                    Ah
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item
                            :label="$t('Individual voltage')"
                            prop="cellVoltage"
                        >
                            <div
                                class="w-full relative flex-1 flex items-center"
                            >
                                <el-input
                                    v-model="fixedCellVoltageDisplay"
                                    class="w-full"
                                    disabled
                                />
                                <div
                                    class="absolute right-4 top-0.5 leading-8 text-secondar-text dark:text-60-dark"
                                >
                                    V
                                </div>
                            </div>
                        </el-form-item>

                        <el-form-item
                            :label="$t('RatedTotalVoltage')"
                            prop="ratedVoltage"
                        >
                            <div
                                class="w-full relative flex-1 flex items-center"
                            >
                                <el-input-number
                                    controls-position="right"
                                    v-model="formState.ratedVoltage"
                                    :precision="3"
                                    :min="0.001"
                                    :max="10000"
                                    class="flex-1"
                                    :disabled="
                                        formMode === 'view' ||
                                        (formMode === 'edit' &&
                                            currentProject?.totalDevices > 0)
                                    "
                                />
                                <div
                                    class="absolute right-10 top-0.5 leading-8 text-secondar-text dark:text-60-dark"
                                >
                                    V
                                </div>
                            </div>
                        </el-form-item>

                        <el-form-item
                            :label="$t('Rated capacity')"
                            prop="ratedCapacity"
                        >
                            <div
                                class="w-full relative flex-1 flex items-center"
                            >
                                <el-input-number
                                    controls-position="right"
                                    v-model="formState.ratedCapacity"
                                    :precision="3"
                                    :min="0.001"
                                    :max="10000"
                                    class="flex-1"
                                    :disabled="
                                        formMode === 'view' ||
                                        (formMode === 'edit' &&
                                            currentProject?.totalDevices > 0)
                                    "
                                />
                                <div
                                    class="absolute right-10 top-0.5 leading-8 text-secondar-text dark:text-60-dark"
                                >
                                    Ah
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item
                            :label="$t('Rated energy')"
                            prop="ratedEnergy"
                        >
                            <div
                                class="w-full relative flex-1 flex items-center"
                            >
                                <el-input-number
                                    controls-position="right"
                                    v-model="formState.ratedEnergy"
                                    :precision="3"
                                    :min="0.001"
                                    :max="10000"
                                    class="flex-1"
                                    :disabled="
                                        formMode === 'view' ||
                                        (formMode === 'edit' &&
                                            currentProject?.totalDevices > 0)
                                    "
                                />
                                <div
                                    class="absolute right-10 top-0.5 leading-8 text-secondar-text dark:text-60-dark"
                                >
                                    kWh
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item
                            :label="$t('Initiation date')"
                            prop="createDate"
                        >
                            <el-date-picker
                                v-model="formState.createDate"
                                type="date"
                                :placeholder="$t('Initiation date')"
                                value-format="YYYY-MM-DD"
                                style="width: 100%"
                                :disabled="
                                    formMode === 'view' ||
                                    (formMode === 'edit' &&
                                        currentProject?.totalDevices > 0)
                                "
                            />
                        </el-form-item>
                        <el-form-item
                            :label="$t('Project description')"
                            prop="description"
                        >
                            <el-input
                                v-model="formState.description"
                                type="textarea"
                                :maxlength="200"
                                :rows="4"
                                :placeholder="$t('Project description')"
                                :disabled="formMode === 'view'"
                            />
                        </el-form-item>
                    </div>
                </el-form>
            </div>
        </el-drawer>

        <!-- 添加设备弹窗 -->
        <el-drawer
            v-model="addDeviceVisible"
            :size="486"
            :show-close="false"
            @close="cancelAdd"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{ $t('add_device') }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="cancelAdd">{{
                            $t('Cancle')
                        }}</el-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            @click="confirmAddDevice()"
                            >{{ $t('Confirm') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <div class="forms">
                <el-form
                    ref="AddFormRef"
                    :model="formData"
                    label-width="120px"
                    label-position="left"
                >
                    <el-form-item
                        :label="$t('Belonging project')"
                        prop="projectNo"
                        :label-width="itemLabelWidth"
                    >
                        <el-select
                            v-model="formData.projectNo"
                            :placeholder="$t('Belonging project')"
                            class="w-full"
                            disabled
                        >
                            <el-option
                                v-for="item in projectData"
                                :key="item.projectNo"
                                :label="item.projectName"
                                :value="item.projectNo"
                            />
                        </el-select>
                    </el-form-item>
                    <div class="mb-2 text-primary-text dark:text-80-dark">
                        {{ $t('Device info') }}
                    </div>
                    <!-- 电池列表 -->
                    <div
                        v-for="(item, index) in formData.batteryList"
                        :key="index"
                        class="bg-background dark:bg-ffffff-dark px-3 py-4 mb-3 rounded"
                    >
                        <div class="flex items-center justify-between mb-1">
                            <div
                                class="font-medium text-title dark:text-title-dark"
                            >
                                {{ $t('Vehicles') }}{{ index + 1 }}
                            </div>
                        </div>

                        <el-form-item
                            :label="$t('Device No')"
                            :prop="`batteryList.${index}.sn`"
                        >
                            <el-input
                                v-model="item.sn"
                                :placeholder="$t('Device No')"
                            />
                        </el-form-item>
                        <el-form-item
                            :label="$t('BatteryNo')"
                            :prop="`batteryList.${index}.batteryNo`"
                        >
                            <el-input
                                v-model="item.batteryNo"
                                :placeholder="$t('BatteryNo')"
                            />
                        </el-form-item>

                        <div
                            class="flex justify-center items-center gap-x-3 text-xs"
                        >
                            <el-button
                                @click="copyBatteryRow(item, index)"
                                plain
                                round
                                size="small"
                                type="primary"
                            >
                                {{ $t('Copy') }}
                            </el-button>
                            <el-button
                                v-if="formData.batteryList.length > 1"
                                @click="removeBatteryRow(index)"
                                round
                                size="small"
                                linear
                            >
                                {{ $t('Delete') }}
                            </el-button>
                        </div>
                    </div>

                    <!-- 添加更多设备按钮 -->
                    <div class="flex justify-center mt-4">
                        <el-button
                            @click="addBatteryRow"
                            type="primary"
                            plain
                            round
                        >
                            {{ $t('add_device') }}
                        </el-button>
                    </div>
                </el-form>
            </div>
        </el-drawer>
        <comfirm-pop
            :title="$t('Are you sure to delete：')"
            :content="`${deleteRow?.projectName}`"
            :visible="deleteDialogVisible"
            @confirm="confirmDeleteProject"
            @update:visible="deleteDialogVisible = $event"
        />
    </div>
</template>

<script setup>
import {
    ref,
    shallowRef,
    computed,
    watch,
    reactive,
    onMounted,
    toRaw,
} from 'vue'
import {
    ElPopover,
    ElCheckbox,
    ElButton,
    ElIcon,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
} from 'element-plus'
import {
    Search,
    CloseBold,
    Plus,
    Delete,
    View,
    Setting,
    Operation,
    Filter,
    MoreFilled,
} from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import _cloneDeep from 'lodash/cloneDeep'
import { useI18n } from 'vue-i18n'
import { useStore } from 'vuex'
import dayjs from 'dayjs'
import powerApi from '@/apiService/power'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import iconSvg from '@/components/svgIcon'
import Decimal from 'decimal.js'
import debounce from 'lodash/debounce'

const { t, locale } = useI18n()
const store = useStore()
const router = useRouter()

// 移除不再需要的缓存相关代码，因为现在使用 el-table 的 template 插槽

/**
 * 无障碍访问优化说明：
 *
 * 1. 为筛选按钮添加了 role="button" 和 tabindex="0" 属性，使其可以通过键盘访问
 * 2. 添加了键盘事件处理（Enter 和 Space 键）
 * 3. 添加了 focus-visible 样式，提供清晰的焦点指示
 * 4. 使用标准的 el-popover 配置，确保弹窗正常显示
 *
 * 这些优化提高了无障碍访问体验，同时保持功能正常
 */

// 添加设备弹窗所需变量
const formData = reactive({
    projectNo: '',
    batteryList: [{ sn: '', batteryNo: '' }],
})
const addDeviceLoading = ref(false)
const projectData = ref([])

const addBatteryRow = () => {
    formData.batteryList.push({ sn: '', batteryNo: '' })
}

const removeBatteryRow = (index) => {
    if (formData.batteryList.length > 1) {
        formData.batteryList.splice(index, 1)
    }
}

const copyBatteryRow = (item, index) => {
    const newRow = _cloneDeep(item)
    formData.batteryList.splice(index + 1, 0, newRow)
}

// Props定义

// 表格数据 - 使用 shallowRef 减少深度响应式开销，提升滚动性能
const tableData = shallowRef([])
const tableLoading = ref(false)

const projectPageTotal = ref(0)
const projectPageInfo = ref({
    current: 1,
    size: 10,
})
const getProjectData = async () => {
    try {
        tableLoading.value = true
        const { sortField, sortOrder } = sortBy.value
        let params = {
            status: undefined,
            model: modelFilter.value || undefined,
            vehicleType: vehicleTypeFilter.value || undefined,
            cellType: cellTypeFilter.value || undefined,
            powerType: powerTypeFilter.value || undefined,
            projectId: undefined,
            customerId: undefined,
            ...projectPageInfo.value,
            sortField: sortField == 'createDate' ? 'createTime' : sortField,
            sortOrder,
        }
        if (searchValueP.value) {
            if (searchTypeP.value == 'no') {
                params.projectNo = searchValueP.value
                params.projectName = undefined
            } else if (searchTypeP.value == 'name') {
                params.projectName = searchValueP.value
                params.projectNo = undefined
            }
        } else {
            params.projectName = undefined
            params.projectNo = undefined
        }

        let res = await powerApi.getProjectPageList(params)
        tableData.value = res.data.data.records.map((item) => {
            return {
                ...item,
                ...item.bmsSummaryInfo,
                // totalCapacity: new Decimal(item.bmsSummaryInfo?.totalDevices).mul(
                //     new Decimal(item.ratedPower)
                // ),
            }
        })
        projectPageTotal.value = res.data.data.total
        tableLoading.value = false
    } catch (error) {
        tableLoading.value = false
        console.error('获取项目数据失败:', error)
    }
}

// watch(
//     () => props.data,
//     (newVal, oldVal) => {
//         tableData.value = newVal
//     },
//     { immediate: true, deep: true }
// )
// 列配置相关
const modelFilter = ref('')
const selectedModels = ref([])
const vehicleTypeFilter = ref([])
const cellTypeFilter = ref([])
const powerTypeFilter = ref([])

// 本地存储的 key
const COLUMN_SETTINGS_KEY = 'project_table_columns'

// 从本地存储读取列设置
const loadColumnSettings = () => {
    try {
        const savedColumns = localStorage.getItem(COLUMN_SETTINGS_KEY)
        if (savedColumns) {
            const parsedColumns = JSON.parse(savedColumns)
            // 验证存储的数据是否有效
            if (Array.isArray(parsedColumns) && parsedColumns.length > 0) {
                // 确保操作列始终存在
                if (!parsedColumns.includes('Operation')) {
                    parsedColumns.push('Operation')
                }
                selectedColumns.value = parsedColumns
                return true
            }
        }
    } catch (error) {
        console.error('Failed to load column settings:', error)
    }
    return false
}

// 保存列设置到本地存储
const saveColumnSettings = (columns) => {
    try {
        localStorage.setItem(COLUMN_SETTINGS_KEY, JSON.stringify(columns))
    } catch (error) {
        console.error('Failed to save column settings:', error)
    }
}

// 列配置
const columnList = ref([
    {
        key: 'projectName',
        title: t('Project name'),
        fixed: 'left',
    },
    { key: 'majorCustomers', title: t('Main customer') },
    { key: 'totalDevices', title: t('Total devices') },
    { key: 'activeCount', title: t('Activated devices') },
    { key: 'onlineCount', title: t('Online devices') },
    { key: 'model', title: t('Device model') },
    { key: 'vehicleType', title: t('Vehicle type') },
    { key: 'vehicleModel', title: t('Vehicle model') },
    { key: 'cellType', title: t('Cell type') },
    { key: 'powerType', title: t('Power type') },
    { key: 'city', title: t('Region') },
    {
        key: 'totalCapacity',
        title: t('Total capacity') + '(Ah)',
    },
    { key: 'totalEnergy', title: t('TotalEnergy') + '(kWh)' },
    {
        key: 'chgTimeSum',
        title: t('Charging duration') + '(h)',
    },
    { key: 'chgCapSum', title: t('Charging amount') + '(Ah)' },
    {
        key: 'dsgTimeSum',
        title: t('Discharging duration') + '(h)',
    },
    {
        key: 'dsgCapSum',
        title: t('Discharging amount') + '(Ah)',
    },
    { key: 'totalAlarms', title: t('Alarm count') },
    { key: 'createDate', title: t('Initiation date') },
    { key: 'Operation', title: t('Operation'), fixed: 'right' },
])

const checkedColumns = ref([...columnList.value.map((col) => col.key)])
const selectedColumns = ref([
    'projectName',
    'majorCustomers',
    'totalDevices',
    'model',
    'vehicleType',
    'vehicleModel',
    'cellType',
    'powerType',
    'chgCapSum',
    'dsgCapSum',
    'createDate',
    'Operation',
])
const dialogVisible = ref(false)
const draggabledColumns = ref(columnList.value)

// 计算可见的列
function getResultArr(arr, rulesArr) {
    const map = arr.reduce((acc, item) => {
        acc[item.key] = item
        return acc
    }, {})
    return rulesArr.map((key) => map[key]).filter((item) => item !== undefined)
}

const visibleColumns = computed(() => {
    return getResultArr(columnList.value, selectedColumns.value)
})

// 列选择相关函数
const onChangeCheckedColumns = (e) => {
    // 确保操作列始终被选中
    if (!e.includes('Operation')) {
        e.push('Operation')
    }

    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
    if (e.length == columnList.value.length) {
        checkAll.value = true
        isIndeterminate.value = false
    } else if (e.length == 0) {
        checkAll.value = false
        isIndeterminate.value = false
    } else {
        isIndeterminate.value = true
    }
}

// 显示列选择器
const showColumnSelector = () => {
    checkedColumns.value = visibleColumns.value.map((col) => col.key)
    // 确保操作列始终被选中
    if (!checkedColumns.value.includes('Operation')) {
        checkedColumns.value.push('Operation')
    }
    draggabledColumns.value = visibleColumns.value.map((col) => col)
    dialogVisible.value = true
}

const closeDrawer = () => {
    dialogVisible.value = false
}

const onSave = () => {
    selectedColumns.value = draggabledColumns.value.map((col) => col.key)
    // 确保操作列始终在最后
    if (!selectedColumns.value.includes('Operation')) {
        selectedColumns.value.push('Operation')
    } else {
        // 如果操作列已存在，将其移到最后
        const operationIndex = selectedColumns.value.indexOf('Operation')
        if (operationIndex > -1) {
            selectedColumns.value.splice(operationIndex, 1)
            selectedColumns.value.push('Operation')
        }
    }
    dialogVisible.value = false
    ElMessage.success(t('Successed'))
    // 保存列设置到本地存储
    saveColumnSettings(selectedColumns.value)
}

const checkAll = ref(true)
const isIndeterminate = ref(true)

const handleCheckAllChange = (val) => {
    checkedColumns.value = val
        ? columnList.value.map((col) => col.key)
        : ['projectName', 'majorCustomers', 'Operation']
    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
    isIndeterminate.value = false
}

const onInvert = () => {
    const requiredColumns = ['projectName', 'majorCustomers', 'Operation']
    const currentChecked = [...checkedColumns.value]
    const allColumns = columnList.value.map((col) => col.key)

    // 反选逻辑：保留必选项，其他项取反
    checkedColumns.value = [
        ...requiredColumns,
        ...allColumns.filter(
            (key) =>
                !requiredColumns.includes(key) && !currentChecked.includes(key)
        ),
    ]

    // 更新拖拽列表
    draggabledColumns.value = columnList.value.filter((item) => {
        return checkedColumns.value.includes(item.key)
    })
}

// 重置到默认设置
const resetToDefault = () => {
    const defaultColumns = [
        'projectName',
        'majorCustomers',
        'totalDevices',
        'model',
        'vehicleType',
        'cellType',
        'powerType',
        'chgCapSum',
        'dsgCapSum',
        'createDate',
        'Operation',
    ]

    selectedColumns.value = [...defaultColumns]
    checkedColumns.value = [...defaultColumns]
    draggabledColumns.value = columnList.value.filter((item) => {
        return defaultColumns.includes(item.key)
    })

    // 清除本地存储
    localStorage.removeItem(COLUMN_SETTINGS_KEY)

    ElMessage.success(t('Successed'))
}

// 处理拖拽结束
const handleDragEnd = ({
    newDraggableIndex,
    newIndex,
    oldDraggableIndex,
    oldIndex,
}) => {
    // 可以在这里处理拖拽结束后的逻辑
}

const checkMove = (e) => {
    // 获取拖拽后的目标索引
    const targetIndex = e.draggedContext.futureIndex
    // 如果目标位置是前两个，则禁止拖拽
    return targetIndex >= 2
}

const onDeleteItem = (item) => {
    const index = draggabledColumns.value.findIndex(
        (col) => col.key === item.key
    )
    if (index < 2) {
        return
    }
    if (index > -1) {
        draggabledColumns.value.splice(index, 1)
        const checkedIndex = checkedColumns.value.indexOf(item.key)
        if (checkedIndex > -1) {
            checkedColumns.value.splice(checkedIndex, 1)
        }
    }
}

// 移除列选择相关函数，因为现在使用固定的 el-table-column

// 移除不再需要的排序状态枚举，因为现在使用 el-table 的排序

// 处理排序变化
const sortBy = ref({
    sortField: undefined,
    sortOrder: undefined,
})

const handleSortChange = ({ column, prop, order }) => {
    // 构建排序参数
    console.log(column, prop, order)
    sortBy.value = order
        ? {
              sortField: prop === 'createDate' ? 'createTime' : prop,
              sortOrder: order === 'ascending' ? 'asc' : 'desc',
          }
        : {
              sortField: undefined,
              sortOrder: undefined,
          }

    // 更新请求参数并重新获取数据
    debouncedGetProjectData()
}

// 防抖版本的获取数据函数
const debouncedGetProjectData = debounce(async () => {
    await getProjectData()
}, 300)

// 移除不再需要的排序图标函数，因为现在使用 el-table 的排序

// 移除不再需要的拖拽和列选择相关代码，因为现在使用固定的 el-table-column

const vehicleTypes = computed(
    () => store.state.dictionary.dictionaries.vehicleType || []
)
const getVehicleType = (val) => {
    if (vehicleTypes.value.length == 0) return
    if (!val) return '-'
    return vehicleTypes.value.find((item) => item.value == val).label
}

const getVehicleModel = (val) => {
    if (vehicleModelOptions.value.length == 0) return
    if (!val) return '-'
    const option = vehicleModelOptions.value.find((item) => item.value == val)
    return option ? option.label : val
}

// 添加
const formState = reactive({
    projectNo: '',
    projectName: '',
    model: '',
    vehicleType: '',
    vehicleModel: '',
    powerType: '',
    cellType: '',
    cellPack: '',
    tanks: undefined,
    series: undefined,
    parallels: undefined,
    cellCapacity: undefined,
    ratedVoltage: undefined,
    cellVoltage: 3.2, // 固定值3.2V
    ratedCapacity: undefined,
    ratedEnergy: undefined,
    description: '',
    createDate: dayjs().format('YYYY-MM-DD'),
})

const rules = {
    projectName: [
        {
            required: true,
            message:
                t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('Project name'),
            trigger: 'blur',
        },
    ],
    projectNo: [
        {
            required: true,
            message:
                t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('Project No'),
            trigger: 'blur',
        },
        {
            validator: async (rule, value, callback) => {
                if (!value) {
                    callback()
                    return
                }
                try {
                    if (formMode.value === 'add') {
                        const res = await powerApi.getProjectBasicInfo({
                            projectNo: value,
                        })
                        if (res.data.code === 0 && res.data.data) {
                            callback(new Error('项目编号重复'))
                        } else {
                            callback()
                        }
                    }
                } catch (error) {
                    console.error('项目编号验证失败:', error)
                    callback()
                }
            },
            trigger: 'change',
        },
    ],
    model: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    vehicleType: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    vehicleModel: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    powerType: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    cellType: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    cellPack: [
        {
            required: true,
            message: t('placeholder_qingxuanze'),
            trigger: 'change',
        },
    ],
    tanks: [
        {
            required: true,
            message:
                t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('Battery box count'),
            trigger: 'blur',
        },
    ],
    series: [
        {
            required: true,
            message:
                t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('Battery series count'),
            trigger: 'blur',
        },
    ],
    parallels: [
        {
            required: true,
            message:
                t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('Battery parallel count'),
            trigger: 'blur',
        },
    ],
    cellCapacity: [
        {
            required: true,
            message:
                t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('Individual capacity'),
            trigger: 'blur',
        },
    ],
    ratedVoltage: [
        {
            required: true,
            message:
                t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('RatedTotalVoltage'),
            trigger: 'blur',
        },
    ],
    ratedCapacity: [
        {
            required: true,
            message:
                t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('Rated capacity'),
            trigger: 'blur',
        },
    ],
    ratedEnergy: [
        {
            required: true,
            message:
                t('placeholder_qingshuru') +
                (locale.value == 'zh' ? '' : ' ') +
                t('Rated energy'),
            trigger: 'blur',
        },
    ],
    description: [
        { max: 200, message: t('message_zuidazhichichangdu'), trigger: 'blur' },
    ],
}
const modelOptionsSearchValue = ref('')
const onSearchM = (e) => {}
const modelOptions = computed(() => {
    let arr = store.state.dictionary.dictionaries.powerBmsModel
    return arr.filter((item) => {
        return item.label.includes(modelOptionsSearchValue.value)
    })
})

const allModelOptions = computed(() => {
    return store.state.dictionary.dictionaries.powerBmsModel
})

const addNewOption = async () => {
    const params = {
        type: 'powerBmsModel',
        itemValue: modelOptionsSearchValue.value,
    }
    const res = await powerApi.addDictItem(params)
    if (res.data.data) {
        ElMessage.success(t('Successed'))
        store.commit('dictionary/SET_DICTIONARY', {
            type: 'powerBmsModel',
            data: undefined,
        })
    }
    try {
        await store.dispatch('dictionary/getDictionary', 'powerBmsModel')
        let re = modelOptions.value.find((item) => {
            return item.label == modelOptionsSearchValue.value
        })
        formState.model = re.value
    } catch (error) {
        //
    } finally {
        //
    }
}

const vehicleTypeOptions = computed(
    () => store.state.dictionary.dictionaries.vehicleType || []
)

// 车辆型号搜索和管理
const vehicleModelSearchValue = ref('')
const vehicleModelOptions = computed(
    () => store.state.dictionary.dictionaries.vehicleModel || []
)

const filteredVehicleModelOptions = computed(() => {
    if (!formState.vehicleType) return []

    let options = vehicleModelOptions.value.filter(
        (item) => item.parentValue === formState.vehicleType
    )

    if (vehicleModelSearchValue.value) {
        options = options.filter((item) =>
            item.label.includes(vehicleModelSearchValue.value)
        )
    }

    return options
})

// 固定选项定义
const fixedPowerTypeOptions = [
    { value: 'electric', label: '纯电动' },
    { value: 'hybrid', label: '混合动力' },
    { value: 'fuel_cell', label: '燃料电池' },
]

const fixedCellTypeOptions = [
    { value: 'lithium_iron_phosphate', label: '磷酸铁锂' },
    { value: 'ternary_lithium', label: '三元锂' },
    { value: 'lithium_titanate', label: '钛酸锂' },
]

const fixedCellPackOptions = [
    { value: 'cylindrical', label: '圆柱形' },
    { value: 'prismatic', label: '方形' },
    { value: 'pouch', label: '软包' },
]

// 保留原有选项用于筛选
const powerTypeOptions = computed(
    () => store.state.dictionary.dictionaries.powerType || []
)

const cellTypeOptions = computed(
    () => store.state.dictionary.dictionaries.cellType || []
)

const cellPackOptions = computed(
    () => store.state.dictionary.dictionaries.cellPack || []
)

const selectedVehicleTypes = ref([])
const selectedCellTypes = ref([])
const selectedPowerTypes = ref([])

// 筛选弹窗显示状态
const vehicleTypeFilterVisible = ref(false)
const cellTypeFilterVisible = ref(false)
const powerTypeFilterVisible = ref(false)

// 筛选处理函数
const onVehicleTypeFilterChange = (value, checked) => {
    if (checked) {
        if (!selectedVehicleTypes.value.includes(value)) {
            selectedVehicleTypes.value.push(value)
        }
    } else {
        const index = selectedVehicleTypes.value.indexOf(value)
        if (index > -1) {
            selectedVehicleTypes.value.splice(index, 1)
        }
    }
}

const onVehicleTypeFilterReset = () => {
    selectedVehicleTypes.value = []
    vehicleTypeFilter.value = []
    debouncedGetProjectData()
    vehicleTypeFilterVisible.value = false
}

const onVehicleTypeFilterConfirm = () => {
    vehicleTypeFilter.value = selectedVehicleTypes.value
    debouncedGetProjectData()
    vehicleTypeFilterVisible.value = false
}

const onCellTypeFilterChange = (value, checked) => {
    if (checked) {
        if (!selectedCellTypes.value.includes(value)) {
            selectedCellTypes.value.push(value)
        }
    } else {
        const index = selectedCellTypes.value.indexOf(value)
        if (index > -1) {
            selectedCellTypes.value.splice(index, 1)
        }
    }
}

const onCellTypeFilterReset = () => {
    selectedCellTypes.value = []
    cellTypeFilter.value = []
    debouncedGetProjectData()
    cellTypeFilterVisible.value = false
}

const onCellTypeFilterConfirm = () => {
    cellTypeFilter.value = selectedCellTypes.value
    debouncedGetProjectData()
    cellTypeFilterVisible.value = false
}

const onPowerTypeFilterChange = (value, checked) => {
    if (checked) {
        if (!selectedPowerTypes.value.includes(value)) {
            selectedPowerTypes.value.push(value)
        }
    } else {
        const index = selectedPowerTypes.value.indexOf(value)
        if (index > -1) {
            selectedPowerTypes.value.splice(index, 1)
        }
    }
}

const onPowerTypeFilterReset = () => {
    selectedPowerTypes.value = []
    powerTypeFilter.value = []
    debouncedGetProjectData()
    powerTypeFilterVisible.value = false
}

const onPowerTypeFilterConfirm = () => {
    powerTypeFilter.value = selectedPowerTypes.value
    debouncedGetProjectData()
    powerTypeFilterVisible.value = false
}

// 固定单体电压显示值
const fixedCellVoltageDisplay = computed(() => '3.2')

// 计算衍生值的函数
const calculateDerivedValues = () => {
    if (formState.series && formState.parallels && formState.cellCapacity) {
        // 额定总压 = 单体电压 * 电池串数 * 电池并数
        formState.ratedVoltage = Number(
            (3.2 * formState.series * formState.parallels).toFixed(3)
        )

        // 额定容量 = 单体容量 * 电池串数 * 电池并数
        formState.ratedCapacity = Number(
            (
                formState.cellCapacity *
                formState.series *
                formState.parallels
            ).toFixed(3)
        )

        // 额定能量 = 额定总压 * 额定容量 / 1000 (转换为kWh)
        formState.ratedEnergy = Number(
            ((formState.ratedVoltage * formState.ratedCapacity) / 1000).toFixed(
                3
            )
        )
    }
}

// 车辆类型变化处理
const onVehicleTypeChange = () => {
    // 清空车辆型号
    formState.vehicleModel = ''
    vehicleModelSearchValue.value = ''
}

// 车辆型号搜索处理
const onSearchVehicleModel = () => {
    // 搜索逻辑已在computed中处理
}

// 添加新的车辆型号选项
const addNewVehicleModelOption = async () => {
    if (!formState.vehicleType || !vehicleModelSearchValue.value) return

    const params = {
        type: 'vehicleModel',
        itemValue: vehicleModelSearchValue.value,
        parentValue: formState.vehicleType,
    }

    try {
        const res = await powerApi.addDictItem(params)
        if (res.data.data) {
            ElMessage.success(t('Successed'))
            store.commit('dictionary/SET_DICTIONARY', {
                type: 'vehicleModel',
                data: undefined,
            })
            await store.dispatch('dictionary/getDictionary', 'vehicleModel')

            // 设置新创建的选项为当前值
            const newOption = filteredVehicleModelOptions.value.find(
                (item) => item.label === vehicleModelSearchValue.value
            )
            if (newOption) {
                formState.vehicleModel = newOption.value
            }
        }
    } catch (error) {
        console.error('添加车辆型号失败:', error)
    }
}

onMounted(async () => {
    // 先加载列设置，再获取数据
    const hasLoadedSettings = loadColumnSettings()

    await getProjectData()
    await getProjectList()
    store.dispatch('dictionary/getDictionary', 'powerBmsModel')
    store.dispatch('dictionary/getDictionary', 'vehicleType')
    store.dispatch('dictionary/getDictionary', 'vehicleModel')
    store.dispatch('dictionary/getDictionary', 'powerType')
    store.dispatch('dictionary/getDictionary', 'cellType')
    store.dispatch('dictionary/getDictionary', 'cellPack')

    // 如果没有加载到存储的设置，使用默认设置
    if (!hasLoadedSettings) {
        console.log('Using default column settings')
    }
})
const projectPageChange = async () => {
    await getProjectData()
}

// 防抖版本的分页切换函数
const debouncedProjectPageChange = debounce(async () => {
    await getProjectData()
}, 300)

const handleProjectSizeChange = (e) => {
    projectPageInfo.value.size = e
    debouncedProjectPageChange()
}
const onSearchP = async (e) => {
    await getProjectData(e)
}

// 防抖版本的搜索函数
const debouncedOnSearchP = debounce(async (e) => {
    await getProjectData(e)
}, 300)
const addVisible = ref(false)

// 操作相关的响应式变量
const viewConfigVisible = ref(false)
const addDeviceVisible = ref(false)
const currentProject = ref(null)
const formMode = ref('add') // 'add', 'view', 'edit'

const drawerTitle = computed(() => {
    if (formMode.value === 'add') return t('Add project')
    if (formMode.value === 'view') return t('Project detail')
    if (formMode.value === 'edit') return t('Project detail')
    return ''
})

const onEdit = () => {
    formMode.value = 'edit'
}

const itemLabelWidth = computed(() => {
    let res =
        locale.value == 'zh'
            ? '120px'
            : locale.value == 'en'
            ? '160px'
            : '140px'
    return res
})
const addProject = () => {
    Object.keys(formState).forEach((key) => {
        formState[key] = ''
    })
    formState.createDate = dayjs().format('YYYY-MM-DD')
    formState.tanks = undefined
    formState.series = undefined
    formState.parallels = undefined
    formState.ratedPower = undefined
    formState.ratedVoltage = undefined
    formState.cellVoltage = undefined
    formState.ratedCurrent = undefined
    formState.ratedCapacity = undefined
    formState.ratedEnergy = undefined
    formRef.value?.clearValidate()
    formMode.value = 'add'
    addVisible.value = true
}
const cancelAdd = () => {
    if (addVisible.value) {
        addVisible.value = false
    } else if (addDeviceVisible.value) {
        addDeviceVisible.value = false
        formData.projectNo = ''
        formData.batteryList = [{ sn: '', batteryNo: '' }]
    }
}
const formRef = ref(null)
const AddFormRef = ref(null)
const addLoading = ref(false)
const confirmProject = async () => {
    if (!formRef.value) return
    formRef.value.validate().then(async () => {
        let params = {
            ...toRaw(formState),
        }
        if (formMode.value === 'edit') {
            let res = await powerApi.editProject({
                ...params,
                id: projectId.value,
            })
            if (res.data.code === 0) {
                ElMessage.success(t('Successed'))
                cancelAdd()
                await getProjectData()
            }
        } else {
            let res = await powerApi.createProject(params)
            if (res.data.code === 0) {
                ElMessage.success(t('Successed'))
                cancelAdd()
                await getProjectData()
            }
        }
    })
}

const confirmAddDevice = async () => {
    if (!formData.projectNo) {
        return ElMessage.warning('Please select a project')
    }
    const devicesToAdd = formData.batteryList.filter((d) => d.sn)
    if (devicesToAdd.length === 0) {
        return ElMessage.warning('Please add at least one device with a SN')
    }

    const project = projectData.value.find(
        (p) => p.projectNo === formData.projectNo
    )
    if (!project) {
        return ElMessage.error('Invalid project selected')
    }

    addDeviceLoading.value = true
    try {
        let res = await powerApi.batchAddBattery({
            projectNo: formData.projectNo,
            batteryList: formData.batteryList,
        })
        if (res.data.data) {
            ElMessage.success(t('Successed'))
            addDeviceVisible.value = false
            await getProjectData() // Refresh table
        }
    } catch (error) {
        ElMessage.error('Failed to add devices')
    } finally {
        addDeviceLoading.value = false
    }
}

const searchTypeP = ref('no')
const searchValueP = ref()
const onChangeSearchTypeP = () => {}
const searchTypesP = ref([
    { value: 'no', label: t('Project No') },
    {
        value: 'name',
        label: t('Project name'),
    },
])

const emit = defineEmits(['search'])

const supplierId = computed(() => {
    return store.state.device.selectSupplierInfo?.id
})
const projectId = ref()
const rowClick = (rowData) => {
    console.log(rowData)
    if (rowData.totalDevices > 0) {
        router.push({
            name: 'equipmentDetail',
            query: {
                sn: rowData.sn,
                supplierId: supplierId.value,
                projectId: rowData.id,
            },
        })
    } else {
        formMode.value = 'view'
        Object.keys(formState).forEach((key) => {
            formState[key] = rowData[key]
        })
        projectId.value = rowData.id
        currentProject.value = rowData
        addVisible.value = true
        // ElMessage.error(t('该项目下暂无设备'))
    }
}
const labelWidth = computed(() => {
    let res =
        locale.value == 'zh' ? '72px' : locale.value == 'en' ? '158px' : '140px'
    return res
})

// 移除不再需要的 onDeleteItem 函数，因为现在使用固定的 el-table-column
const deleteDialogVisible = ref(false)
const deleteRow = ref()
const handleOperation = (command, rowData) => {
    currentProject.value = rowData

    switch (command) {
        case 'viewConfig':
            // 打开查看配置弹窗
            formMode.value = 'view'
            Object.keys(formState).forEach((key) => {
                formState[key] = rowData[key]
            })
            projectId.value = rowData.id
            addVisible.value = true
            break
        case 'addDevice':
            // 打开添加设备弹窗，自动设置项目编号
            formData.projectNo = rowData.projectNo
            formData.batteryList = [{ sn: '', batteryNo: '' }]
            addDeviceVisible.value = true
            break
        case 'deleteRecord':
            // 删除记录确认
            deleteRow.value = rowData
            deleteDialogVisible.value = true
            // ElMessageBox.confirm(
            //     `${t('Are you sure to delete：')}${rowData.projectNo}?`,
            //     t('Delete'),
            //     {
            //         confirmButtonText: t('Confirm'),
            //         cancelButtonText: t('Cancle'),
            //         type: 'warning',
            //         roundButton: true,
            //     }
            // )
            //     .then(async () => {
            //         try {
            //             // 调用删除API
            //             let res = await powerApi.deleteProject({
            //                 id: rowData.id,
            //             })
            //             if (res.data.data) {
            //                 ElMessage.success(t('Successed'))
            //                 // 刷新数据
            //                 await getProjectData()
            //             }
            //         } catch (error) {
            //             ElMessage.error(t('Failed, please try again later'))
            //         }
            //     })
            //     .catch(() => {
            //         // 用户取消删除
            //     })
            break
        default:
            console.error('Unknown command:', command)
    }
}
const confirmDeleteProject = async () => {
    try {
        // 调用删除API
        let res = await powerApi.deleteProject({
            id: deleteRow.value.id,
        })
        if (res.data.data) {
            ElMessage.success(t('Successed'))
            // 刷新数据
            await getProjectData()
        }
    } catch (error) {
        ElMessage.error(t('Failed, please try again later'))
    }
}

// 添加设备相关
const addDeviceFormRef = ref(null)

const getProjectList = async () => {
    try {
        let res = await powerApi.getProjectPageList({ current: 1, size: 1000 })
        if (res.data.code === 0) {
            projectData.value = res.data.data.records
        }
    } catch (error) {
        console.error('Failed to fetch project list:', error)
    }
}

// 防抖函数：获取项目基本信息
const debouncedGetProjectInfo = debounce(async (projectNo) => {
    if (!projectNo) return

    // try {
    //     const res = await powerApi.getProjectBasicInfo({ projectNo })
    //     if (res.data.code === 0 && res.data.data) {
    //         // 接口调用成功，说明项目编号已存在，显示错误提示
    //         // ElMessage.error('项目编号重复')
    //     } else {
    //         // 项目编号不存在，清除错误提示
    //         console.warn('项目编号不存在或获取失败')
    //     }
    // } catch (error) {
    //     console.error('获取项目基本信息失败:', error)
    // }
}, 300)

// 项目编号变化处理函数
const handleProjectNoChange = () => {
    debouncedGetProjectInfo(formState.projectNo)
}
</script>

<style scoped lang="less">
.custom-table {
    width: 100%;
    // height: 100%;
    // height: calc(100vh - 200px);
    height: calc(~'100vh - 240px');
    position: relative;
    .table-header {
        position: absolute;
        width: 100%;
        left: 0;
        top: -32px;
    }
}

// .table-box {
//     height: 670px;
// }

.filter-wrapper {
    // padding: 8px;

    .filter-group {
        // margin-bottom: 8px;
    }

    .filter-options {
        // margin-bottom: 8px;

        :deep(.el-checkbox) {
            display: flex;
            margin-right: 0;
            // margin-bottom: 8px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.column-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #eee;
    justify-content: space-between;
}

.drag-handle {
    cursor: move;
    margin-right: 8px;
    color: #909399;
    width: 24px;
    height: 24px;
    font-size: 24px;
}
.drag-handle-disabled {
    cursor: not-allowed;
    margin-right: 8px;
    color: #909399;
    width: 24px;
    height: 24px;
    font-size: 24px;
    opacity: 0.4;
}

:deep(.el-checkbox) {
    margin-right: 0;
    width: 100%;
}

:deep(.el-checkbox.is-disabled) {
    cursor: not-allowed;
}
:deep(.el-input-number) {
    width: 100%;
    text-align: left;
    .el-input__inner {
        text-align: left;
    }
}

:deep(.fixed-input .el-input__wrapper) {
    padding-right: 80px;
}
:deep(.hasFilter) {
    color: var(--themeColor);
}

/* 无障碍访问优化 */
:deep([role='button']) {
    outline: none;
}

:deep([role='button']:focus-visible) {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 2px;
    border-radius: 4px;
}
:deep(.el-button:focus-visible) {
    outline: none !important;
}

:deep(.el-dropdown) {
    display: inline-block;
}

:deep(.el-dropdown-menu) {
    z-index: 3000;
}

:deep(.el-dropdown-item) {
    display: flex;
    align-items: center;
    padding: 8px 16px;
}
.view-content {
    height: calc(100vh - 200px);
    position: relative;
}
.operation-drop {
    :deep([role='button']:focus-visible) {
        outline: none !important;
    }
}
</style>
