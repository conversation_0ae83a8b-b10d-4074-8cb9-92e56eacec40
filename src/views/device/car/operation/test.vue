<template>
    <div>
        <el-input :value="modelValue" @input="updateVal"></el-input>
    </div>
</template>

<script setup>
import { ref } from 'vue'
const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
})
const emit = defineEmits(['update:modelValue'])
const updateVal = (field, value) => {
    console.log(field, value, field)

    emit('update:modelValue', field)
}
</script>

<style lang="less" scoped></style>
