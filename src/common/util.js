
/**
 * 格式化金额
 * @param price 需要格式化的金额
 * @param n 保留的小数位数 默认2位
 * @returns {string}
 */
export function formattermoney(price, n) {
  if (isNaN(parseFloat(price))) {
    return "";
  }
  n = n > 0 && n <= 20 ? n : 2;
  price = parseFloat((price + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
  let l = price
    .split(".")[0]
    .split("")
    .reverse(),
    r = price.split(".")[1];
  let t = "";
  for (let i = 0; i < l.length; i++) {
    t += l[i] + ((i + 1) % 3 == 0 && i + 1 != l.length ? "," : "");
  }
  return {
    l:
      t
        .split("")
        .reverse()
        .join("") + ".",
    r: r,
  };
}

// export function dingTalkLoginApi() {
//   return new Promise(function (resolve, reject) {
//     const tag = document.getElementsByTagName("script");
//     for (let i of tag) {
//       if (
//         i.src ===
//         "https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js"
//       ) {
//         resolve(window.DTFrameLogin);
//         return;
//       }
//     }
//     const script = document.createElement("script");
//     script.type = "text/javascript";
//     script.src =
//       "https://g.alicdn.com/dingding/h5-dingtalk-login/0.21.0/ddlogin.js";
//     script.onerror = reject;
//     document.body.appendChild(script);
//     script.onload = () => {
//       resolve(window.DTFrameLogin);
//     };
//   });
// }
//转换成万元单位


/**
 * 数字调整
 * eg: 1234 => 2000
 */


export function uuidv4() {
  return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, (c) =>
    (
      c ^
      (crypto.getRandomValues(new Uint8Array(1))[0] & (15 >> (c / 4)))
    ).toString(16)
  );
}

export const chargeState = [
  {
    label: 'Charge',
    name: 'status_chongdian',
    value: "2",
    icon: "icon-ica-dianchi-chongdian",
    color: "#85E5C2",
    backGroundColor: "#85E5C2",
  },
  {
    label: 'Discharge',
    name: 'status_fangdian',
    value: "1",
    icon: "icon-ica-dianchi-fangdian",
    color: "#73ADFF",
    backGroundColor: "#73ADFF",
  },
  {
    label: "status_daiji",
    name: "status_daiji",
    value: "0",
    icon: "icon-ica-dianchi-yunhang",
    color: "#FCAA6B",
    backGroundColor: "#FCAA6B",
  },
  {
    label: 'status_lixian',
    name: 'status_lixian',
    value: "3",
    icon: "icon-ica-dianchi-lixian",
    color: "#FD0B0B",
    backGroundColor: "#666666",
  },
];

export const chargeStateP = [
  {
    label: 'Charge',
    name: 'status_chongdian',
    value: "1",
    icon: "icon-ica-dianchi-chongdian",
    color: "#85E5C2",
    backGroundColor: "#85E5C2",

  },
  {
    label: 'Discharge',
    name: 'status_fangdian',
    value: "2",
    icon: "icon-ica-dianchi-fangdian",
    color: "#73ADFF",
    backGroundColor: "#73ADFF",
  },
  {
    label: "status_daiji",
    name: "status_daiji",
    value: "0",
    icon: "icon-ica-dianchi-yunhang",
    color: "#FCAA6B",
    backGroundColor: "#FCAA6B",
  },
  {
    label: 'status_lixian',
    name: 'status_lixian',
    value: "3",
    icon: "icon-ica-dianchi-lixian",
    color: "#FD0B0B",
    backGroundColor: "#666666",
  },
  {
    label: 'NotActivated',
    name: 'NotActivated',
    value: -1,
    icon: 'icon-ica-dianchi-lixian',
    color: 'var(--text-60)',
    backGroundColor: '#666666',
  },
];
export const getState = (status, type) => {
  if (type == 'power') {
    const item = chargeStateP.find((s) => s.value == status) || {};
    return item;
  } else {
    const item = chargeState.find((s) => s.value == status) || {};
    return item;
  }

};
export const deviceActiveStatus = [
  {
    label: 'Activated',
    name: 'Activated',
    value: 1,
    icon: 'icon-ica-dianchi-fangdian',
    color: '#73ADFF',
    backGroundColor: '#73ADFF',
  },
  {
    label: 'NotActivated',
    name: 'NotActivated',
    value: 0,
    icon: 'icon-ica-dianchi-lixian',
    color: '#666666',
    backGroundColor: '#666666',
  },
]
export const formatterActiveStatus = (e) => {
  const item = deviceActiveStatus.find((s) => s.value == e) || {}
  return item.name
}

export const hexToRgba = (hex, alpha = 1.0) => {
  // 去掉开头的#号，如果有的话
  hex = hex.replace(/^#/, '');
  // 处理3位数的hex颜色码
  if (hex.length === 3) {
    hex = hex.split('').map(hex => hex + hex).join('');
  }
  // 解析r、g、b值
  const bigint = parseInt(hex, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;

  // 返回rgba字符串
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

export const setThemeColor = (themeColor) => {
  let color
  if (themeColor) {
    color = themeColor
  } else {
    color = '#6FBECE'
  }
  if (localStorage.getItem('cn-sys-theme') === 'dark') {
    color = '#ffffff'
  }
  const el = document.documentElement
  getComputedStyle(el).getPropertyValue(`--el-color-primary`)
  getComputedStyle(el).getPropertyValue('--el-color-primary-light-2')
  getComputedStyle(el).getPropertyValue('--el-color-primary-dark-2')
  getComputedStyle(el).getPropertyValue('--el-color-primary-light-3')
  getComputedStyle(el).getPropertyValue('--el-color-primary-light-5')
  getComputedStyle(el).getPropertyValue('--el-color-primary-light-7')
  getComputedStyle(el).getPropertyValue('--el-color-primary-light-8')
  getComputedStyle(el).getPropertyValue('--el-color-primary-light-9')
  // getComputedStyle(el).getPropertyValue('--el-text-color-regular')
  // getComputedStyle(el).getPropertyValue('--el-upload-list-picture-card-size')  
  getComputedStyle(el).getPropertyValue('--el-border-color-hover')  // 输入框鼠标划过颜色
  el.style.setProperty('--el-color-primary', color)
  el.style.setProperty('--el-color-primary-light-2', hexToRgba(color, 0.8))
  el.style.setProperty('--el-color-primary-dark-2', hexToRgba(color, 0.8))
  el.style.setProperty('--el-color-primary-light-3', hexToRgba(color, 0.7))
  el.style.setProperty('--el-color-primary-light-5', hexToRgba(color, 0.5))
  el.style.setProperty('--el-color-primary-light-7', hexToRgba(color, 0.3))
  el.style.setProperty('--el-color-primary-light-8', hexToRgba(color, 0.2))
  el.style.setProperty('--el-color-primary-light-9', hexToRgba(color, 0.1))
  el.style.setProperty('--el-border-color-hover', color)
}
export const getSegmentTypeColor = (type) => {
  const colors = [
    {
      color: "rgba(252, 32, 52, 1)",
      borderBottom: "1px solid rgba(252, 32, 52, 1)",
      background: 'linear-gradient( 180deg, rgba(106,242,114,0) 0%, rgba(252,32,52,0.16) 100%)',
      startColor: 'rgba(252,32,52,0.16)',
      endColor: 'rgba(106,242,114,0)',
    },
    {
      color: "rgba(253, 116, 0, 1)",
      borderBottom: "1px solid rgba(253, 116, 0, 1)",
      background: ' linear-gradient( 180deg, rgba(106,242,114, 0) 0%, rgba(253,116,0,0.16) 100%)',
      startColor: 'rgba(253,116,0,0.16)',
      endColor: 'rgba(106,242,114,0)',
    },
    {
      color: "rgba(255, 217, 46, 1)",
      borderBottom: "1px solid rgba(255, 217, 46, 1)",
      background: 'linear-gradient( 180deg, rgba(106,242,114, 0) 0%, rgba(255,231,124,0.16) 100%)',
      startColor: ' rgba(255,231,124,0.16)',
      endColor: 'rgba(106,242,114,0)',
    },
    {
      color: "rgba(43, 180, 48, 1)",
      borderBottom: "1px solid rgba(43, 180, 48, 1)",
      background: 'linear-gradient( 180deg, rgba(106,242,114, 0) 0%, rgba(43, 180, 48, 0.16) 100%)',
      startColor: 'rgba(43, 180, 48, 0.16)',
      endColor: 'rgba(106,242,114,0)',
    },
    {
      color: "rgba(111, 190, 206, 1)",
      borderBottom: "1px solid rgba(111, 190, 206, 1)",
      background: 'linear-gradient( 180deg, rgba(106,242,114, 0) 0%, rgba(111,190,206,0.16) 100%)',
      startColor: 'rgba(111,190,206,0.16)',
      endColor: 'rgba(106,242,114,0)',
    }
  ]
  return colors[type - 1]
}
export const setBusinessType = (val) => {
  let homeRoute = ''
  // if (val == 'vehicle_battery') {
  //   homeRoute = '/vehicle'
  //   if (!localStorage.getItem('activeSystem')) {
  //     localStorage.setItem('activeSystem', 'car')
  //   }
  // } else {
  //   homeRoute = '/device'
  //   if (!localStorage.getItem('activeSystem')) {
  //     localStorage.setItem('activeSystem', 'device')
  //   }
  // }

  if (val == 'energy_storage_cabinet') {
    homeRoute = '/device'
    if (!localStorage.getItem('activeSystem')) {
      localStorage.setItem('activeSystem', 'device')
    }
  } else {
    homeRoute = '/vehicle'
    if (!localStorage.getItem('activeSystem')) {
      localStorage.setItem('activeSystem', 'car')
    }

  }



  localStorage.setItem('homeRoute', homeRoute)  // 存储初始路由
  localStorage.setItem('businessType', val)   // 存储权限
}

export const getThemeColor = (varName) => {
  return getComputedStyle(document.documentElement)
    .getPropertyValue(varName)
    .trim()
}

// ECharts 颜色映射表
export const echartsColorVars = {
  title: '--echart-title',
  line: '--echart-line',
  itemBorder: '--echart-item-border',
  carPieBorder: '--car-pie-border',
}
export const download = ({ url, fileName, callBack }) => {
  fetch(url, {
    method: "get",
    mode: "cors",
  })
    .then((response) => response.blob())
    .then((data) => {
      const downloadUrl = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
    });
}
export const getBrowserLanguage = () => {
  const lang =
    navigator.language ||
    (navigator.languages && navigator.languages[0])
  if (lang.startsWith('zh')) {
    return 'zh'
  } else if (lang.startsWith('en')) {
    return 'en'
  }
  return 'other'
}
import axios from 'axios'
export const getPublicIP = async () => {
  try {
    const res = await axios.get('https://api.ipify.org?format=json')
    return res.data.ip
  } catch (error) {
    console.error('获取公网 IP 失败', error)
    return null
  }
}
export const getCountry = async () => {
  try {
    const res = await axios.get('https://ipapi.co/json/')
    return res.data.country_name // 如 "China", "United States"
  } catch (error) {
    console.error('获取国家信息失败', error)
    return null
  }
}
export const serviceStatus = [
  {
    label: 'Un Configured',
    value: '0'
  }, {
    label: 'Normal',
    value: 1
  }, {
    label: 'Expired',
    value: 2
  }, {
    label: 'Near Expire',
    value: 3
  },
]
export const formatterServiceStatus = (val) => {
  const item = serviceStatus.find((s) => s.value == val) || {};
  return item;
}