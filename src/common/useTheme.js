import { ref, watchEffect, reactive } from 'vue'
import { setThemeColor, setBusinessType, echartsColorVars } from '@/common/util.js'
import hooks from '../views/role/components/hooks'
import { useStore } from 'vuex'
import deviceService from '@/apiService/device'
import _cloneDeep from 'lodash/cloneDeep'
export default function useTheme() {
  const store = useStore()
  // const isDark = ref(localStorage.getItem('cn-sys-theme') === 'dark')
  const isDark = ref(store.state.theme.isDark)
  const themeColors = _cloneDeep(echartsColorVars)
  const themeChangeComplete = ref(true)
  watchEffect(() => {
    themeChangeComplete.value = false

    // 更新主题相关的 DOM 类名和存储
    // localStorage.setItem('cn-sys-theme', isDark.value ? 'dark' : 'light')
    // document.documentElement.classList.toggle('dark', isDark.value)
    // document.documentElement.classList.toggle('light', !isDark.value)
    document.documentElement.classList.toggle('light', true)

    // 更新主题颜色变量
    setTimeout(() => {
      themeColors.title = getComputedStyle(document.documentElement)
        .getPropertyValue('--echart-title').trim()
      themeColors.line = getComputedStyle(document.documentElement)
        .getPropertyValue('--echart-line').trim()
      themeColors.itemBorder = getComputedStyle(document.documentElement)
        .getPropertyValue('--echart-item-border').trim()
      themeColors.carPieBorder = getComputedStyle(document.documentElement)
        .getPropertyValue('--car-pie-border').trim()
      themeChangeComplete.value = true
    }, 300)
  })

  //   获取企业配置主题色
  const getApi = async () => {
    const host = window.location.host
    const token = store.getters['user/getNewToken']
    console.log(token);
    
    if (token) {
      return await deviceService.getCurrentOrgData()
    } else {
      return await deviceService.getOrgWebpageSettingByDomain({
        domain: host,
      })
    }
  }

  const toggleTheme = async (event) => {
    if (!document.startViewTransition) {
      // 浏览器不支持 View Transitions API，直接切换主题
      isDark.value = !isDark.value
      store.commit('theme/SET_THEME', isDark.value)
      const { data: { code, data } } = await getApi()
      setThemeColor(isDark.value ? '#ffffff' : data?.themeColor || hooks.config.themeColor)
      return
    }

    // 使用 View Transitions API 实现平滑过渡
    const { clientX: x, clientY: y } = event || {
      clientX: window.innerWidth - 20,  // 距离右边缘 20px
      clientY: 20  // 距离顶部 20px
    }
    const transition = document.startViewTransition(async () => {
      isDark.value = !isDark.value
      store.commit('theme/SET_THEME', isDark.value)
      const { data: { code, data } } = await getApi()
      setThemeColor(isDark.value ? '#ffffff' : data?.themeColor || hooks.config.themeColor)
    })

    transition.ready.then(() => {
      const endRadius = Math.hypot(
        Math.max(x, window.innerWidth - x),
        Math.max(y, window.innerHeight - y)
      )
      document.documentElement.animate(
        {
          clipPath: [`circle(0px at ${x}px ${y}px)`, `circle(${endRadius}px at ${x}px ${y}px)`]
        },
        {
          duration: 500,
          easing: "ease-in-out",
          pseudoElement: "::view-transition-new(root)"
        }
      )
    })
  }

  return { isDark, toggleTheme, themeColors, themeChangeComplete }
}