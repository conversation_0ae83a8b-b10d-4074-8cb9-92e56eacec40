import api from "@/api";
import store from "@/store";
import { Get, Post, Delete, RequestUse } from "@/common/request";
function applyJoinSupplier(param) {
  return Post({
    url: api.applyJoinSupplier,
    bodyParam: param,
    config: {
      tokenType: "platform",
    },
  });
}
function sysSupplierCreate(param) {
  return Post({
    url: api.sysSupplierCreate,
    bodyParam: {
      ...param,
      // orgAuthenticationPlatform: "tianyancha",
    },
    config: {
      tokenType: "platform",
    },
  });
}
function changeTenant(tenantId) {
  return Get({
    url: api.changeTenant,
    urlParam: {
      tenantId,
    },
    config: {
      headers: {
        authTerminal: "Basic ZXMtaXN2LXBjOmVzLWlzdi1wYy1wYXNz",
      },
    },
  });
}
function enterMerchantCenter(tenantId) {
  return Get({
    url: api.enterMerchantCenter,
    urlParam: {
      tenantId,
    },
    config: {
      tokenType: "platform",
      headers: {
        authTerminal: "Basic ZXMtaXN2LXBjOmVzLWlzdi1wYy1wYXNz",
      },
    },
  });
}
function platformLoginAndRegister(param) {
  return Post({
    url: api.platformLoginAndRegister,
    bodyParam: {
      ...param,
      projectName: "MING_ENTERPRISE_SERVICE",
    },
    config: {
      needToken: false,
      headers: {
        Authorization:
          "Basic ZW50ZXJwcmlzZS1zZXJ2aWNlLXdlYjplbnRlcnByaXNlLXNlcnZpY2Utd2ViLXBhc3M=",
      },
    },
  });
}

function thirdUserBindPlatformUser(param) {
  return Post({
    url: api.thirdUserBindPlatformUser,
    bodyParam: {
      projectName: "MING_ENTERPRISE_SERVICE",
      platform: "weChat",
      ...param,
    },
    config: {
      needToken: false,
    },
  });
}
//获取用户userId下的商户列表
function queryUserSupplierList() {
  return Get({
    url: api.queryUserSupplierList,
    config: {
      tokenType: "platform",
    },
  });
}
function queryStaffSupplierList() {
  return Get({
    url: api.queryStaffSupplierList,
  });
}
function listRoleWithPermission() {
  return Get({
    url: api.listRoleWithPermission,
  });
}
function getStaffList() {
  return Get({
    url: api.queryCurrentOrgStaffList,
  });
}
function getManager() {
  return Get({
    url: api.queryCurrentOrgManager,
  });
}
function getUserByPhone(phone) {
  return Get({
    url: api.searchUserByPhoneForInvite,
    pathParam: { phone },
  });
}
function addMember(param) {
  return Post({
    url: api.addStaffAndRole,
    bodyParam: param,
  });
}
function changeManager(param) {
  return Post({
    url: api.authAndModifyOrgManager,
    bodyParam: param,
  });
}
function updateStaffRole(param) {
  return Post({
    url: api.modifyStafRole,
    bodyParam: param,
  });
}
// 供应商所有员工列表包含管理员
function getEmployeeList() {
  return Get({
    url: api.queryEmployeeList,
  });
}

function getSmsCode(phone, smsVerifyType) {
  return Post({
    url: api.getSmsCode,
    bodyParam:
      encodeURIComponent("phone") +
      "=" +
      encodeURIComponent(phone) +
      "&" +
      encodeURIComponent("smsVerifyType") +
      "=" +
      encodeURIComponent(smsVerifyType),
    config: {
      needToken: false,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    },
  });
}
function supplyerCreate(param) {
  return Post({
    url: api.supplyerCreate,
    bodyParam: param,
  });
}
function getUserInfo() {
  return Get({
    url: api.getUserInfo,
  });
}
function queryUserSupplier() {
  return Get({
    url: api.queryUserSupplier,
  });
}
function supplierModify(param) {
  return Post({
    url: api.supplierModify,
    bodyParam: param,
  });
}
function staffModify(param) {
  return Post({
    url: api.staffModify,
    bodyParam: param,
  });
}
function verifySmsCode(param) {
  return Post({
    url: api.verifySmsCode,
    bodyParam: param,
  });
}
function fileUpload(file, sence) {
  let formData = new FormData();
  formData.append("file", file);
  return Post({
    url: api.fileUpload.replace("{sence}", sence),
    bodyParam: formData,
    config: {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    },
  });
}

function getCenterInformation() {
  return Get({
    url: api.getCenterInformation,
  });
}

function centerInformationUpdate(param) {
  return Post({
    url: api.centerInformationUpdate,
    bodyParam: param,
  });
}

function logout() {
  return Delete({
    url: api.logout,
  });
}
function queryTycOrgBasicInfo(keyword) {
  return Get({
    url: api.queryTycOrgBasicInfo,
    pathParam: { keyword },
    config: {
      tokenType: "platform",
    },
  });
}
function searchAddressAssociate(addressKeywords) {
  return Get({
    url: api.searchAddressAssociate,
    urlParam: { addressKeywords },
  });
}
function getAddressDivisionCode(addressText) {
  return Get({
    url: api.getAddressDivisionCode,
    urlParam: { addressText },
  });
}
function queryOrgBankAccount(orgId) {
  return Get({
    url: api.queryOrgBankAccount,
    pathParam: { orgId },
  });
}
function addOrgBankAccount(param) {
  return Post({
    url: api.addOrgBankAccount,
    bodyParam: param,
  });
}
function modifyOrgBankAccount(param) {
  return Post({
    url: api.modifyOrgBankAccount,
    bodyParam: param,
  });
}
function DDFreeAuthenLogin(code, thirdOrgId) {
  return Post({
    url: api.DDFreeAuthenLogin,
    bodyParam: {
      code,
      projectName: "MING_ENTERPRISE_SERVICE",
      thirdUserType: "dingTalk",
      platform: "ES_SUPPLIER_DING_TALK_APP",
      thirdOrgId,
    },
    config: {
      needToken: false,
    },
  });
}
function dingTalkUserAuthAcquirePhone({
  thirdUserIdentify,
  thirdOrgId,
  authCode,
}) {
  return Post({
    url: api.dingTalkUserAuthAcquirePhone,
    bodyParam: {
      projectName: "MING_ENTERPRISE_SERVICE",
      platform: "ES_SUPPLIER_DING_TALK_APP",
      thirdUserIdentify,
      thirdOrgId,
      authCode,
    },
    config: {
      needToken: false,
    },
  });
}
function merchantLoginAndRegister(param) {
  return Post({
    url: api.merchantLoginAndRegister,
    bodyParam: {
      projectName: "MING_ENTERPRISE_SERVICE",
      loginType: "4",
      ...param,
    },
    config: {
      needToken: false,
      headers: {
        Authorization: "Basic ZXMtaXN2LXBjOmVzLWlzdi1wYy1wYXNz",
      },
    },
  });
}
function getVersion() {
  return Get({
    url:
      "https://ming-enterprise-oss.mingwork.com/clientVersion/clientVersion.json?stamp=" +
      Date.now(),
  });
}
function getAgreements(name) {
  let company = process.env.VUE_APP_COMPANY || "mingwork";
  return Get({
    url: `https://ming-enterprise.oss-cn-hangzhou.aliyuncs.com/static/agreements/${company}/${name}.html?stamp=${Date.now()}`,
  });
}
function getWxQrCodeByType(param) {
  return Post({
    url: api.getWxQrCodeByType,
    bodyParam: param,
    config: {
      needToken: false,
    },
  });
}
function orgShareUrl(bizId, staffId, bizType) {
  return Get({
    url: api.orgShareUrl,
    urlParam: { bizId, staffId, bizType },
  });
}
function addSupplierOperator(param) {
  return Post({
    url: api.addSupplierOperator,
    bodyParam: param,
  });
}
function getSupplierOperatorList() {
  return Get({
    url: api.getSupplierOperatorList,
  });
}

function leaveStaff(staffId) {
  return Post({
    url: api.leaveStaff,
    bodyParam:
      encodeURIComponent("staffId") + "=" + encodeURIComponent(staffId),
  });
}
function createAndUpdateOrg(obj) {
  return Post({
    url: api.createAndUpdateOrg,
    bodyParam: obj,
  });
}
function selectSupplierPage(searchParam) {
  return Get({
    url: api.selectSupplierPage,
    urlParam: { ...searchParam },
  });
}
function selectFranchiserList() {
  return Get({
    url: api.selectFranchiserList,
  });
}
function getAddedServiceDetail(param) {
  return Get({
    url: api.getAddedServiceDetail,
    urlParam: { param },
  });
}
function getDictByType(param) {
  return Get({
    url: api.getDictByType,
    urlParam: param,
  });
}
const langs = [
  {
    label: '中文',
    lang: 'zh',
    value: 'zh-CN'
  },
  {
    label: 'English',
    lang: 'en',
    value: 'en-US'
  }
]
const getLang = (lang) => {
  const res = langs.filter(item => item.lang == lang)
  if (res.length) {
    return res[0].value
  }
  return 'zh-CN'
}
function getMessages(lang) {
  return Get({
    url: api.getMessages,
    config: {
      //  needToken: false,
      headers: {
        'accept-language': getLang(lang),
      }
    }
  });
}
function getLoginCurrentUserDetail(param) {
  return Get({
    url: api.getLoginCurrentUserDetail,
  });
}
function updateMeInfo(params) {
  return Post({
    url: api.updateMeInfo,
    bodyParam: params,
  });
}
function modifySubOrgWithOwner(params) {
  return Post({
    url: api.modifySubOrgWithOwner,
    bodyParam: params,
  });
}
function delOrg(orgId) {
  return Post({
    url: api.delOrg,
    pathParam: { orgId }
  });
}


export default {
  selectSupplierPage,
  selectFranchiserList,
  createAndUpdateOrg,
  leaveStaff,
  getSupplierOperatorList,
  addSupplierOperator,
  getWxQrCodeByType,
  getVersion,
  getAgreements,
  merchantLoginAndRegister,
  dingTalkUserAuthAcquirePhone,
  DDFreeAuthenLogin,
  enterMerchantCenter,
  platformLoginAndRegister,
  thirdUserBindPlatformUser,
  queryUserSupplierList,
  queryStaffSupplierList,
  getStaffList,
  getManager,
  getUserByPhone,
  getSmsCode,
  addMember,
  changeManager,
  updateStaffRole,
  supplyerCreate,
  getUserInfo,
  queryUserSupplier,
  supplierModify,
  verifySmsCode,
  fileUpload,
  getEmployeeList,
  getCenterInformation,
  centerInformationUpdate,
  logout,
  listRoleWithPermission,
  sysSupplierCreate,
  applyJoinSupplier,
  queryTycOrgBasicInfo,
  staffModify,
  searchAddressAssociate,
  getAddressDivisionCode,
  queryOrgBankAccount,
  addOrgBankAccount,
  modifyOrgBankAccount,
  changeTenant,
  orgShareUrl,
  getAddedServiceDetail,
  getDictByType,
  getLoginCurrentUserDetail,
  updateMeInfo,
  getMessages,
  modifySubOrgWithOwner,
  delOrg,
};
