# DeviceFilterForm 组件

这是一个可复用的设备筛选表单组件，用于筛选设备列表。

## 功能特性

-   设备型号多选筛选
-   地区树形多选筛选
-   激活时间范围筛选
-   支持表单验证
-   支持国际化
-   响应式布局

## Props

| 参数          | 类型    | 默认值 | 说明             |
| ------------- | ------- | ------ | ---------------- |
| deviceOptions | Array   | []     | 设备型号选项列表 |
| cityOptions   | Array   | []     | 地区选项列表     |
| shortcuts     | Array   | []     | 日期快捷选项     |
| loading       | Boolean | false  | 加载状态         |
| initialValues | Object  | {}     | 初始值           |

## Events

| 事件名        | 参数       | 说明               |
| ------------- | ---------- | ------------------ |
| filter-change | filterData | 筛选条件变化时触发 |
| filter-reset  | -          | 重置筛选条件时触发 |

## 使用示例

```vue
<template>
    <DeviceFilterForm
        :device-options="deviceOptions"
        :city-options="cityOptions"
        :shortcuts="shortcuts"
        :loading="loading"
        :initial-values="formState"
        @filter-change="handleFilterChange"
        @filter-reset="handleFilterReset"
    />
</template>

<script setup>
import DeviceFilterForm from '@/components/DeviceFilterForm.vue'

const handleFilterChange = (filterData) => {
    console.log('筛选条件:', filterData)
    // 处理筛选逻辑
}

const handleFilterReset = () => {
    console.log('重置筛选条件')
    // 处理重置逻辑
}
</script>
```

## 筛选数据格式

```javascript
{
  model: [], // 设备型号数组
  cities: [], // 地区数组
  createTimeRange: [startDate, endDate] // 时间范围
}
```

## 注意事项

1. 组件依赖 Element Plus 组件库
2. 需要配置国际化支持
3. 地区选项需要是树形结构数据
4. 日期格式为 'YYYY-MM-DD'
