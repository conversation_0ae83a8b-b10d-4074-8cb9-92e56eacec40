<template>
    <div class="s-box" :class="activeSystem">
        <div class="dl" @click="goSystem('vehicle')">
            <iconSvg name="powerIcon" class="w-5 h-5" />
        </div>
        <div class="cn" @click="goSystem('device')">
            <iconSvg name="deviceIcon" class="w-5 h-5" />
        </div>
        <div class="bg"></div>
    </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'

const activeSystem = ref(localStorage.getItem('activeSystem'))
// const activeSystem = computed(() => {
//     return localStorage.getItem('activeSystem')
// })
const route = useRoute(),
    router = useRouter()
const goSystem = (path) => {
    if (activeSystem.value == 'car' && path == 'vehicle') {
        return
    } else if (activeSystem.value == 'device' && path == 'device') {
        return
    }
    router.push(`/${path}`)
}
watch(
    () => route.path,
    () => {
        activeSystem.value = localStorage.getItem('activeSystem')
    },
    { immediate: true }
)
</script>

<style lang="less" scoped>
.s-box {
    width: 64px;
    height: 32px;
    background: var(--bg-f5);
    // background: #000fff;
    border-radius: 16px;
    position: relative;
    display: flex;
    align-items: center;
    padding: 2px;
    transition: all 0.3s;
    .dl,
    .cn {
        width: 30px;
        height: 28px;
        cursor: pointer;
        user-select: none;
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0.5;
    }
    .bg {
        width: 28px;
        height: 28px;
        background: var(--themeColor);
        border-radius: 14px;
        position: absolute;
        left: 2px;
        top: 2px;
        transition: all 0.3s;
        z-index: 1;
    }
    &.car {
        .dl {
            color: #fff;
            opacity: 1;
        }
        .bg {
            left: 3px;
        }
    }
    &.device {
        .cn {
            color: #fff;
            opacity: 1;
        }
        .bg {
            left: 32px;
        }
    }
}
</style>
