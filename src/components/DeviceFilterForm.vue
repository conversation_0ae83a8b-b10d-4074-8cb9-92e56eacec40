<template>
    <div class="device-filter-form">
        <el-form ref="formRef" :label-width="labelWidth" label-position="left">
            <el-form-item :label="$t('Device model')" prop="model">
                <el-select
                    :model-value="model"
                    @update:model-value="handleModelChange"
                    :placeholder="$t('placeholder_qingxuanze')"
                    multiple
                    clearable
                    collapse-tags
                    collapse-tags-tooltip
                    style="width: 100%"
                >
                    <el-option
                        v-for="item in deviceOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('Region')" prop="cities">
                <el-tree-select
                    :model-value="cities"
                    @update:model-value="handleCitiesChange"
                    :data="cityOptions"
                    multiple
                    :render-after-expand="true"
                    clearable
                    collapse-tags
                    collapse-tags-tooltip
                    style="width: 100%"
                />
            </el-form-item>
            <el-form-item :label="$t('Activation time')" prop="createTimeRange">
                <el-date-picker
                    :model-value="createTimeRange"
                    @update:model-value="handleTimeRangeChange"
                    type="daterange"
                    range-separator="-"
                    :start-placeholder="$t('common_kaishiriqi')"
                    :end-placeholder="$t('common_jieshuriqi')"
                    value-format="YYYY-MM-DD"
                    :shortcuts="shortcuts"
                    style="width: 100%"
                />
            </el-form-item>
            <!-- <el-form-item>
                <div class="flex gap-2">
                    <el-button
                        type="primary"
                        @click="handleFilter"
                        :loading="loading"
                    >
                        {{ $t('Search') }}
                    </el-button>
                    <el-button @click="handleReset">
                        {{ $t('Reset') }}
                    </el-button>
                </div>
            </el-form-item> -->
        </el-form>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t, locale } = useI18n()

// Props定义 - 每个字段独立接收
const props = defineProps({
    model: {
        type: Array,
        default: () => [],
    },
    cities: {
        type: Array,
        default: () => [],
    },
    createTimeRange: {
        type: Array,
        default: () => [undefined, undefined],
    },
    deviceOptions: {
        type: Array,
        default: () => [],
    },
    cityOptions: {
        type: Array,
        default: () => [],
    },
    shortcuts: {
        type: Array,
        default: () => [],
    },
    loading: {
        type: Boolean,
        default: false,
    },
})

// Emits定义 - 每个字段独立事件
const emit = defineEmits([
    'update:model',
    'update:cities',
    'update:createTimeRange',
    'filter-change',
    'filter-reset',
])

// 计算属性
const labelWidth = computed(() => {
    return locale.value === 'zh' ? '70px' : '120px'
})

// 表单引用
const formRef = ref(null)

// 处理各个字段的变化
const handleModelChange = (value) => {
    emit('update:model', value)
}

const handleCitiesChange = (value) => {
    emit('update:cities', value)
}

const handleTimeRangeChange = (value) => {
    emit('update:createTimeRange', value)
}

// 筛选处理函数
const handleFilter = () => {
    emit('filter-change', {
        model: props.model,
        cities: props.cities,
        createTimeRange: props.createTimeRange,
    })
}

// 重置处理函数
const handleReset = () => {
    emit('update:model', [])
    emit('update:cities', [])
    emit('update:createTimeRange', [undefined, undefined])
    emit('filter-reset')
}

// 暴露方法给父组件
defineExpose({
    formRef,
})
</script>

<style scoped lang="less">
.device-filter-form {
    .el-form-item {
        margin-bottom: 16px;
    }

    .el-select,
    .el-tree-select,
    .el-date-picker {
        width: 100%;
    }

    .el-form-item__label {
        font-weight: 500;
        color: var(--text-100);
    }

    .el-button {
        min-width: 80px;
    }
}
</style>
